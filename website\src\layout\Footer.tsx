
import { <PERSON> } from "react-router-dom";
import { MapPin, Phone, Mail, Clock, Facebook } from "lucide-react";
import { Logo } from "@/ui/Logo";
import { Container } from "@/ui/Container";
import {
    CONTACT_INFO,
    getPhoneLink,
    getEmailLink,
    getCopyrightText,
    getFormattedOpeningHours,
    getGoogleMapsUrl
} from "@/lib/constants/contact";
import { PRIMARY_AREA, COMPANY_BASE } from "@/lib/constants/locations";

const Footer = () => {
    return (
        <footer className="bg-gray-900 text-white pt-12 pb-6">
            <Container>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
                    {/* Company info */}
                    <div>
                        <div className="flex items-center gap-2 mb-4">
                            <Logo variant="full" className="text-white" />
                        </div>
                        <p className="text-gray-400 text-sm mb-4">
                            Vi skaper varige uterom tilpasset {PRIMARY_AREA.adjectiveForm} unike
                            terreng og klima. Med base på {COMPANY_BASE.name} betjener vi hele
                            regionen med kvalitetsarbeid og personlig service.
                        </p>
                        <div className="flex items-center gap-2">
                            <Facebook className="w-5 h-5 text-blue-500" />
                            <a
                                href={CONTACT_INFO.social.facebook}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-500 hover:text-blue-400 text-sm font-bold"
                            >
                                Følg oss på Facebook
                            </a>
                        </div>
                    </div>

                    {/* Contact info */}
                    <div>
                        <h3 className="text-lg font-semibold mb-4">
                            Kontakt oss
                        </h3>
                        <ul className="space-y-3">
                            <li className="flex items-start gap-3">
                                <MapPin className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                                <a
                                    href={getGoogleMapsUrl()}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="hover:text-green-400"
                                >
                                    <p>{CONTACT_INFO.address.street}</p>
                                    <p>{CONTACT_INFO.address.postalCode} {CONTACT_INFO.address.city}</p>
                                </a>
                            </li>
                            <li className="flex items-center gap-3">
                                <Phone className="w-5 h-5 text-green-500 flex-shrink-0" />
                                <a
                                    href={getPhoneLink()}
                                    className="hover:text-green-400"
                                >
                                    {CONTACT_INFO.phone}
                                </a>
                            </li>
                            <li className="flex items-center gap-3">
                                <Mail className="w-5 h-5 text-green-500 flex-shrink-0" />
                                <a
                                    href={getEmailLink()}
                                    className="hover:text-green-400"
                                >
                                    {CONTACT_INFO.email}
                                </a>
                            </li>
                        </ul>
                    </div>

                    {/* Opening hours */}
                    <div>
                        <h3 className="text-lg font-semibold mb-4">
                            Åpningstider
                        </h3>
                        <ul className="space-y-3">
                            <li className="flex items-start gap-3">
                                <Clock className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                                <div>
                                    <p className="font-medium">
                                        Mandag - Fredag:
                                    </p>
                                    <p>{getFormattedOpeningHours('weekdays')}</p>
                                </div>
                            </li>
                            <li className="flex items-start gap-3">
                                <Clock className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                                <div>
                                    <p className="font-medium">
                                        Lørdag - Søndag:
                                    </p>
                                    <p>{getFormattedOpeningHours('weekend')}</p>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>

                {/* Quick links */}
                <div className="border-t border-gray-800 pt-8 pb-4">
                    <div className="flex flex-col md:flex-row justify-between items-center">
                        <div className="flex flex-wrap justify-center md:justify-start gap-6 mb-4 md:mb-0">
                            <Link
                                to="/"
                                className="text-gray-400 hover:text-white text-sm"
                            >
                                Hjem
                            </Link>
                            <Link
                                to="/hva"
                                className="text-gray-400 hover:text-white text-sm"
                            >
                                Hva vi gjør
                            </Link>
                            <Link
                                to="/prosjekter"
                                className="text-gray-400 hover:text-white text-sm"
                            >
                                Prosjekter
                            </Link>
                            <Link
                                to="/hvem"
                                className="text-gray-400 hover:text-white text-sm"
                            >
                                Hvem er vi
                            </Link>
                            <Link
                                to="/kontakt"
                                className="text-gray-400 hover:text-white text-sm"
                            >
                                Kontakt oss
                            </Link>
                        </div>
                        <div className="text-gray-500 text-sm">
                            {getCopyrightText()}
                        </div>
                    </div>
                </div>

                {/* Privacy links */}
                <div className="flex justify-center md:justify-end mt-4 gap-4">
                    <Link
                        to="/personvern"
                        className="text-gray-500 hover:text-gray-400 text-xs"
                    >
                        Personvern
                    </Link>
                    <Link
                        to="/cookies"
                        className="text-gray-500 hover:text-gray-400 text-xs"
                    >
                        Cookies
                    </Link>
                </div>
            </Container>
        </footer>
    );
};

export default Footer;
