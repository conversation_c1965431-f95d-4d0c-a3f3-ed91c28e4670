import React from 'react';
import { cn  } from '@/lib/utils';

interface TransitionProps {
  children: React.ReactNode;
  show?: boolean;
  appear?: boolean;
  className?: string;
  type?: 'fade' | 'slide-up' | 'slide-down' | 'slide-left' | 'slide-right';
  duration?: 'fast' | 'normal' | 'slow';
}

const Transition: React.FC<TransitionProps> = ({
  children,
  show = true,
  appear = false,
  className,
  type = 'fade',
  duration = 'normal'
}) => {
  const [isVisible, setIsVisible] = React.useState(show && !appear);

  React.useEffect(() => {
    if (show) {
      setIsVisible(true);
    }
  }, [show]);

  const handleTransitionEnd = () => {
    if (!show) {
      setIsVisible(false);
    }
  };

  const baseStyles = 'transition-all';
  
  const durationStyles = {
    fast: 'duration-200',
    normal: 'duration-300',
    slow: 'duration-500'
  };

  const typeStyles = {
    fade: 'opacity-0',
    'slide-up': 'opacity-0 translate-y-4',
    'slide-down': 'opacity-0 -translate-y-4',
    'slide-left': 'opacity-0 translate-x-4',
    'slide-right': 'opacity-0 -translate-x-4'
  };

  const visibleStyles = {
    fade: 'opacity-100',
    'slide-up': 'opacity-100 translate-y-0',
    'slide-down': 'opacity-100 translate-y-0',
    'slide-left': 'opacity-100 translate-x-0',
    'slide-right': 'opacity-100 translate-x-0'
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div
      className={cn(
        baseStyles,
        durationStyles[duration],
        show ? visibleStyles[type] : typeStyles[type],
        className
      )}
      onTransitionEnd={handleTransitionEnd}
      role="presentation"
    >
      {children}
    </div>
  );
};

export { Transition };
export default Transition;