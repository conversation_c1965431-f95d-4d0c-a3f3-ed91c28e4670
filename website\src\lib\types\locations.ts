/**
 * Type definitions for location data
 */

/**
 * Geographic coordinates
 */
export interface Coordinates {
  lat: number;
  lng: number;
}

/**
 * Primary service area information
 */
export interface PrimaryArea {
  name: string;
  displayName: string;
  adjectiveForm: string;
  regionName: string;
  seoKeywords: string[];
  description: string;
  seoDescription: string;
}

/**
 * Company base location information
 */
export interface CompanyBase {
  name: string;
  municipality: string;
  county: string;
  fullAddress: string;
  coordinates: Coordinates;
  phone: string;
  email: string;
}

/**
 * Service area information
 */
export interface ServiceArea {
  name: string;
  slug: string;
  distance: string;
  isCore: boolean;
  coordinates: Coordinates;
  terrainDescription: string;
  seoDescription: string;
  popularServices: string[];
}

/**
 * SEO metadata for a location
 */
export interface LocationSeoMetadata {
  title: string;
  description: string;
  keywords: string[];
  schema: {
    "@context": string;
    "@type": string;
    name: string;
    image: string;
    address: {
      "@type": string;
      addressLocality: string;
      addressRegion: string;
      addressCountry: string;
    };
    geo?: {
      "@type": string;
      latitude: number;
      longitude: number;
    };
    url: string;
    telephone: string;
    priceRange: string;
    areaServed: string;
  };
}
