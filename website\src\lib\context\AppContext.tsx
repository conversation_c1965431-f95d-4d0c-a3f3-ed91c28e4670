import React, { createContext, useContext, useState, ReactNode } from 'react';

// Define the shape of our global state
interface AppState {
  theme: 'light' | 'dark';
  language: 'no' | 'en';
  notifications: Array<{ id: string; message: string; type: 'info' | 'success' | 'error' }>;
}

// Define the context value shape
interface AppContextValue {
  state: AppState;
  setTheme: (theme: 'light' | 'dark') => void;
  setLanguage: (language: 'no' | 'en') => void;
  addNotification: (message: string, type: 'info' | 'success' | 'error') => void;
  removeNotification: (id: string) => void;
}

// Create the context with a default value
const AppContext = createContext<AppContextValue | undefined>(undefined);

// Initial state
const initialState: AppState = {
  theme: 'light',
  language: 'no',
  notifications: [],
};

// Provider component
export const AppProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, setState] = useState<AppState>(initialState);

  // Theme setter
  const setTheme = (theme: 'light' | 'dark') => {
    setState(prev => ({ ...prev, theme }));
    // Also apply theme to document for immediate visual feedback
    document.documentElement.classList.toggle('dark', theme === 'dark');
  };

  // Language setter
  const setLanguage = (language: 'no' | 'en') => {
    setState(prev => ({ ...prev, language }));
  };

  // Add notification
  const addNotification = (message: string, type: 'info' | 'success' | 'error') => {
    const id = Date.now().toString();
    setState(prev => ({
      ...prev,
      notifications: [...prev.notifications, { id, message, type }],
    }));
    
    // Auto-remove notification after 5 seconds
    setTimeout(() => {
      removeNotification(id);
    }, 5000);
  };

  // Remove notification
  const removeNotification = (id: string) => {
    setState(prev => ({
      ...prev,
      notifications: prev.notifications.filter(notification => notification.id !== id),
    }));
  };

  // Context value
  const value: AppContextValue = {
    state,
    setTheme,
    setLanguage,
    addNotification,
    removeNotification,
  };

  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
};

// Custom hook for using the context
export const useApp = (): AppContextValue => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}; 