/**
 * Data constants
 *
 * This file contains all the static data used throughout the application.
 * It serves as a single source of truth for data that was previously imported directly from data files.
 */

import { services } from '@/data/services';
import { recentProjects } from '@/data/projects';
import { testimonials } from '@/data/testimonials';
import { ServiceType, ProjectType, TestimonialType } from '@/lib/types';
import { SERVICE_AREAS } from './site';

// Export the data as constants to avoid direct imports from data files
export const SERVICES: ServiceType[] = services;
export const PROJECTS: ProjectType[] = recentProjects;
export const TESTIMONIALS: TestimonialType[] = testimonials;

// Re-export SERVICE_AREAS for backward compatibility
export { SERVICE_AREAS };

// Map service IDs to their corresponding image category folders
export const SERVICE_IMAGE_CATEGORIES = {
  belegningsstein: 'belegg',
  cortenstaal: 'stål',
  stottemurer: 'støttemur',
  granitt: 'trapp-repo',
  kantstein: 'kantstein',
  platting: 'platting',
  beplantning: 'hekk',
  ferdigplen: 'ferdigplen'
} as const;

// Map from project category to service ID
export const PROJECT_CATEGORY_TO_SERVICE_ID = {
  "Belegningsstein": "belegningsstein",
  "Cortenstål": "cortenstaal",
  "Støttemur": "stottemurer",
  "Platting": "platting",
  "Ferdigplen": "ferdigplen",
  "Kantstein": "kantstein",
  "Trapper og Repoer": "trapp-repo",
  "Hekk og Beplantning": "hekk"
};
