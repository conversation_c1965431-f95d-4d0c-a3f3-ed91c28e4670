/**
 * Environment Files Validation Script
 *
 * This script validates that environment variables are properly set up.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

// Path to environment files
const envPath = path.resolve(projectRoot, 'config/env');

// Environments to check
const environments = ['development', 'staging', 'production'];

// Get required variables from .env.example
function getRequiredVariables() {
  const exampleEnvPath = path.resolve(envPath, '.env.example');
  const exampleEnv = parseEnvFile(exampleEnvPath);
  return Object.keys(exampleEnv);
}

/**
 * Parse an environment file
 * @param {string} filePath - Path to the environment file
 * @returns {Object} - Parsed environment variables
 */
function parseEnvFile(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`File does not exist: ${filePath}`);
    return {};
  }

  try {
    // Read file content
    const content = fs.readFileSync(filePath, 'utf8');
    const env = {};

    // Split by lines and process each line
    const lines = content.split('\n');

    for (const line of lines) {
      // Skip comments and empty lines
      if (line.trim().startsWith('#') || !line.trim()) {
        continue;
      }

      // Parse key-value pairs
      const parts = line.split('=');
      if (parts.length >= 2) {
        const key = parts[0].trim();
        const value = parts.slice(1).join('=').trim();
        env[key] = value;
      }
    }

    return env;
  } catch (error) {
    console.error(`Error reading file ${filePath}: ${error.message}`);
    return {};
  }
}

/**
 * Validate environment files
 * @returns {boolean} - True if all files are valid, false otherwise
 */
function validateEnvironments() {
  let hasWarnings = false;
  const requiredVariables = getRequiredVariables();

  if (requiredVariables.length === 0) {
    console.warn('Warning: No variables found in .env.example');
    hasWarnings = true;
  }

  environments.forEach(env => {
    const envFile = path.resolve(envPath, `.env.${env}`);

    if (!fs.existsSync(envFile)) {
      console.warn(`Warning: Missing environment file for ${env}`);
      hasWarnings = true;
      return;
    }

    const envVars = parseEnvFile(envFile);

    requiredVariables.forEach(variable => {
      if (!envVars[variable]) {
        console.warn(`Warning: Missing required variable ${variable} in ${env} environment`);
        hasWarnings = true;
      }
    });
  });

  return !hasWarnings;
}

/**
 * Main function
 */
function main() {
  console.log('Validating environment files...');

  const isValid = validateEnvironments();

  if (isValid) {
    console.log('✅ All environment files are valid!');
    process.exit(0);
  } else {
    console.error('❌ Found issues in environment files.');
    process.exit(1);
  }
}

// Run the main function
main();
