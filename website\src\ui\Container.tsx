import React from "react";

interface ContainerProps {
    children: React.ReactNode;
    className?: string;
    as?: React.ElementType;
    maxWidth?: "xs" | "sm" | "md" | "lg" | "xl" | "2xl" | "full";
}

const Container: React.FC<ContainerProps> = ({
    children,
    className = "",
    as: Component = "div",
    maxWidth = "xl",
}) => {
    const maxWidthClasses = {
        xs: "max-w-xs",
        sm: "max-w-sm",
        md: "max-w-md",
        lg: "max-w-lg",
        xl: "max-w-7xl",
        "2xl": "max-w-screen-2xl",
        full: "max-w-full",
    };

    return (
        <Component
            className={`mx-auto px-4 sm:px-6 lg:px-8 w-full ${maxWidthClasses[maxWidth]} ${className}`}
        >
            {children}
        </Component>
    );
};

export { Container };
export default Container;