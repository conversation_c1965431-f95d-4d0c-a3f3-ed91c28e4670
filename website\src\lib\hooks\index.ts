/**
 * Central export file for all hooks in the application
 *
 * This file exports all hooks from the lib/hooks directory,
 * providing a single import point for all hook functionality.
 */

// Analytics hooks
export { useAnalytics } from './useAnalytics';

// DOM and event hooks
export { useEventListener } from './useEventListener';
export { useMediaQuery } from './useMediaQuery';

// Data fetching hooks
export { useData } from './useData';

// Seasonal hooks
export { useSeasonalData } from './useSeasonalData';

// Form hooks
import { useState, useCallback } from 'react';

/**
 * Hook for managing form field state including value, validation, and touch state
 */
export const useFormField = (
  initialValue = "",
  validate?: (value: string) => string | undefined
) => {
  const [value, setValue] = useState(initialValue);
  const [error, setError] = useState<string>();
  const [touched, setTouched] = useState(false);

  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      const newValue = e.target.value;
      setValue(newValue);
      if (touched && validate) {
        setError(validate(newValue));
      }
    },
    [touched, validate]
  );

  const handleBlur = useCallback(() => {
    setTouched(true);
    if (validate) {
      setError(validate(value));
    }
  }, [value, validate]);

  return {
    value,
    error,
    touched,
    handleChange,
    handleBlur,
    setValue,
  };
};