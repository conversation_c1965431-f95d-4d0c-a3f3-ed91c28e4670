/**
 * String utility functions
 */

/**
 * Normalize a string by removing accents, converting to lowercase, and removing non-alphanumeric characters
 * This is useful for comparing strings that may contain special characters like æ, ø, å
 * 
 * @param str The string to normalize
 * @returns The normalized string
 */
export const normalizeString = (str: string): string => {
  if (!str) return '';
  
  // Convert to lowercase
  const lowercase = str.toLowerCase();
  
  // Replace Norwegian characters with their ASCII equivalents
  const withoutSpecialChars = lowercase
    .replace(/æ/g, 'ae')
    .replace(/ø/g, 'o')
    .replace(/å/g, 'a')
    .replace(/é/g, 'e')
    .replace(/è/g, 'e')
    .replace(/ê/g, 'e')
    .replace(/ë/g, 'e')
    .replace(/á/g, 'a')
    .replace(/à/g, 'a')
    .replace(/â/g, 'a')
    .replace(/ä/g, 'a')
    .replace(/ó/g, 'o')
    .replace(/ò/g, 'o')
    .replace(/ô/g, 'o')
    .replace(/ö/g, 'o')
    .replace(/ú/g, 'u')
    .replace(/ù/g, 'u')
    .replace(/û/g, 'u')
    .replace(/ü/g, 'u');
  
  // Remove non-alphanumeric characters (except spaces and hyphens)
  return withoutSpecialChars.replace(/[^a-z0-9\s-]/g, '');
};

/**
 * Compare two strings after normalizing them
 * This is useful for comparing strings that may contain special characters like æ, ø, å
 * 
 * @param str1 The first string to compare
 * @param str2 The second string to compare
 * @returns True if the normalized strings are equal, false otherwise
 */
export const normalizedStringCompare = (str1: string, str2: string): boolean => {
  return normalizeString(str1) === normalizeString(str2);
};
