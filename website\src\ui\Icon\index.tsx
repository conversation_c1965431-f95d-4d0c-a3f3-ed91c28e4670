import React from 'react';
import { cn  } from '@/lib/utils';
import * as LucideIcons from 'lucide-react';

// Let's use a simpler approach with specific icons
const Icon: React.FC<{
  name: string;
  size?: 'sm' | 'md' | 'lg' | number;
  className?: string;
}> = ({ name, size = 'md', className }) => {
  // Get the icon component dynamically
  const LucideIcon = (LucideIcons as any)[name];

  if (!LucideIcon) {
    console.warn(`Icon "${name}" not found`);
    return null;
  }

  const sizeMap = {
    sm: 16,
    md: 24,
    lg: 32
  };

  const iconSize = typeof size === 'string' ? sizeMap[size] : size;

  return (
    <span className={cn('inline-flex', className)}>
      <LucideIcon size={iconSize} aria-hidden="true" />
    </span>
  );
};

export { Icon };
export default Icon;