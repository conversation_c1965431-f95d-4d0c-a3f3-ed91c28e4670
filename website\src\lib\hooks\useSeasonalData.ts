/**
 * Hook for accessing seasonal data
 *
 * This hook provides access to the current season and related data,
 * making it easy to build season-aware components.
 */

import { useMemo } from 'react';
import { SeasonType, EnglishSeasonType } from '@/lib/types';
import {
  getCurrentSeason,
  getSeasonDisplayName,
  getEnglishSeasonName
} from '@/lib/utils/filtering';
import {
  SEASONAL_SERVICES,
  SEASONAL_PROJECTS
} from '@/lib/constants/seasonal';

interface SeasonalData {
  /** The current season in Norwegian (vår, sommer, høst, vinter) */
  currentSeason: SeasonType;

  /** The current season in English (spring, summer, fall, winter) */
  currentSeasonEnglish: EnglishSeasonType;

  /** The display name for the current season (Våren, Sommeren, etc.) */
  currentSeasonDisplay: string;

  /** Categories associated with the current season */
  seasonalCategories: readonly string[];

  /** Features associated with the current season */
  seasonalFeatures: readonly string[];

  /** Tags associated with the current season */
  seasonalTags: readonly string[];

  /** Whether the current season is winter */
  isWinter: boolean;

  /** Whether the current season is spring */
  isSpring: boolean;

  /** Whether the current season is summer */
  isSummer: boolean;

  /** Whether the current season is fall */
  isFall: boolean;
}

/**
 * Hook for accessing seasonal data
 *
 * @param overrideSeason Optional season to override the current season
 * @returns Seasonal data for the current or overridden season
 */
export const useSeasonalData = (overrideSeason?: SeasonType): SeasonalData => {
  // Get the current season or use the override
  const season = overrideSeason || getCurrentSeason();

  // Create the seasonal data object
  const seasonalData = useMemo(() => {
    const seasonEnglish = getEnglishSeasonName(season) as EnglishSeasonType;
    const seasonDisplay = getSeasonDisplayName(season);

    // Get seasonal categories and features
    const serviceData = SEASONAL_SERVICES[season] || { categories: [], features: [] };
    const projectData = SEASONAL_PROJECTS[season] || { categories: [], tags: [] };

    return {
      currentSeason: season,
      currentSeasonEnglish: seasonEnglish,
      currentSeasonDisplay: seasonDisplay,
      seasonalCategories: serviceData.categories,
      seasonalFeatures: serviceData.features,
      seasonalTags: projectData.tags,
      isWinter: season === 'vinter',
      isSpring: season === 'vår',
      isSummer: season === 'sommer',
      isFall: season === 'høst'
    };
  }, [season]);

  return seasonalData;
};

export default useSeasonalData;
