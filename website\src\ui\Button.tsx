import React from "react";
import { Link } from "react-router-dom";

type ButtonVariant = "primary" | "secondary" | "outline" | "text";
type ButtonSize = "sm" | "md" | "lg";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    to?: string;
    variant?: "primary" | "secondary" | "outline";
    size?: "sm" | "md" | "lg";
    className?: string;
    children: React.ReactNode;
}

interface LinkButtonProps extends Omit<ButtonProps, "onClick"> {
    to: string;
    external?: boolean;
    fullWidth?: boolean;
    icon?: React.ReactNode;
    iconPosition?: "left" | "right";
}

const getVariantClasses = (variant: ButtonVariant): string => {
    switch (variant) {
        case "primary":
            return "bg-green-500 hover:bg-green-600 text-white shadow-sm";
        case "secondary":
            return "bg-gray-100 hover:bg-gray-200 text-gray-800 shadow-sm";
        case "outline":
            return "bg-transparent border border-green-500 text-green-500 hover:bg-green-50";
        case "text":
            return "bg-transparent text-green-500 hover:text-green-600 hover:bg-green-50";
        default:
            return "bg-green-500 hover:bg-green-600 text-white shadow-sm";
    }
};

const getSizeClasses = (size: ButtonSize): string => {
    switch (size) {
        case "sm":
            return "text-sm py-1.5 px-3";
        case "md":
            return "text-base py-2 px-4";
        case "lg":
            return "text-lg py-2.5 px-5";
        default:
            return "text-base py-2 px-4";
    }
};

const Button: React.FC<ButtonProps> = ({
    to,
    variant = "primary",
    size = "md",
    className = "",
    children,
    ...props
}) => {
    const baseClasses =
        "inline-flex items-center justify-center font-medium rounded-md transition-colors";

    const variantClasses = {
        primary:
            "bg-green-600 text-white hover:bg-green-700 active:bg-green-800",
        secondary:
            "bg-gray-100 text-gray-900 hover:bg-gray-200 active:bg-gray-300",
        outline:
            "border-2 border-green-600 text-green-600 hover:bg-green-50 active:bg-green-100",
    };

    const sizeClasses = {
        sm: "px-3 py-1.5 text-sm",
        md: "px-4 py-2 text-base",
        lg: "px-6 py-3 text-lg",
    };

    const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;

    if (to) {
        return (
            <Link to={to} className={classes}>
                {children}
            </Link>
        );
    }

    return (
        <button type="button" className={classes} {...props}>
            {children}
        </button>
    );
};

const LinkButton: React.FC<LinkButtonProps> = ({
    children,
    to,
    external = false,
    variant = "primary",
    size = "md",
    fullWidth = false,
    icon,
    iconPosition = "left",
    className = "",
    // props are not used but kept for API compatibility
    // @ts-ignore
    ...props
}) => {
    const baseClasses =
        "inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2";
    const variantClasses = getVariantClasses(variant);
    const sizeClasses = getSizeClasses(size);
    const widthClass = fullWidth ? "w-full" : "";

    if (external) {
        return (
            <a
                href={to}
                target="_blank"
                rel="noopener noreferrer"
                className={`${baseClasses} ${variantClasses} ${sizeClasses} ${widthClass} ${className}`}
            >
                {icon && iconPosition === "left" && (
                    <span className="mr-2">{icon}</span>
                )}
                {children}
                {icon && iconPosition === "right" && (
                    <span className="ml-2">{icon}</span>
                )}
            </a>
        );
    }

    return (
        <Link
            to={to}
            className={`${baseClasses} ${variantClasses} ${sizeClasses} ${widthClass} ${className}`}
        >
            {icon && iconPosition === "left" && (
                <span className="mr-2">{icon}</span>
            )}
            {children}
            {icon && iconPosition === "right" && (
                <span className="ml-2">{icon}</span>
            )}
        </Link>
    );
};

export { Button, LinkButton };
export default Button;
