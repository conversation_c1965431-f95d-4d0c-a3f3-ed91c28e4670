# Contact Form Implementation

This document provides technical details about the implementation of the contact form with advanced analytics and SEO tracking capabilities.

## Component Structure

The contact form is implemented in `src/sections/60-contact/index.tsx` and consists of:

1. **Form UI**: Input fields, validation, and submission handling
2. **Analytics Collection**: Client information tracking
3. **Submission Processing**: Integration with Formspree.io

## Key Implementation Details

### Client Information Interface

The `ClientInfo` interface defines all the data points collected:

```typescript
interface ClientInfo {
  // Basic device info
  device: string;
  os: string;
  browser: string;
  browserLanguage: string;
  screenSize: string;
  
  // Time metrics
  timeOnSite: number;
  sessionDuration: number;
  
  // Referral info
  referrer: string;
  entryPage: string;
  currentPage: string;
  currentPageTitle: string;
  
  // Marketing parameters
  utmSource: string;
  utmMedium: string;
  utmCampaign: string;
  utmTerm: string;
  utmContent: string;
  searchKeywords: string;
  
  // Visit metrics
  visitCount: number;
  pagesViewedCount: number;
  isBounce: boolean;
  daysFromFirstVisit: number;
  
  // Performance metrics
  pageLoadTime: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  timeToInteractive: number;
  cumulativeLayoutShift: number;
  
  // Location info
  country: string;
  region: string;
  localTime: string;
  
  // User journey
  lastVisit: string;
  navigationPath: string[];
  maxScrollDepth: number;
  
  // Engagement metrics
  interactionsCount: number;
  ctaClicks: number;
  formInteractions: number;
  timeToFirstInteraction: number;
  
  // Conversion data
  conversionPath: string[];
  timeToConversion: number;
  previousSites: string[];
  returningVisitorConversion: boolean;
}
```

### Data Collection Methods

The component uses several methods to collect client information:

1. **Device Detection**: Uses `navigator.userAgent` to detect device, OS, and browser
2. **Performance Metrics**: Uses the Performance API (`window.performance`)
3. **User Behavior Tracking**: Uses localStorage and sessionStorage
4. **Interaction Tracking**: Uses event listeners for clicks, scrolls, and form interactions
5. **UTM Parameter Extraction**: Parses URL parameters
6. **Search Keyword Detection**: Extracts from referrer or UTM parameters

### Event Listeners

The component sets up several event listeners to track user interactions:

```typescript
// Add event listeners
document.addEventListener('click', trackInteraction);
document.addEventListener('scroll', trackInteraction);
document.addEventListener('click', trackCtaClick);

// Add form interaction listeners to form elements
const formElements = document.querySelectorAll('input, textarea, select');
formElements.forEach(element => {
  element.addEventListener('focus', trackFormInteraction);
});
```

### Form Submission

The form submission process:

1. Validates all form fields
2. Collects client information
3. Formats the data into readable sections
4. Submits to Formspree.io
5. Handles success/error states

```typescript
const response = await fetch('https://formspree.io/f/xqaqbpqp', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  body: JSON.stringify({
    // Form data
    name: formData.name,
    email: formData.email,
    phone: formData.phone,
    address: formData.address,
    message: formData.description,

    // Client information
    _subject: CONTACT_INFO.form.defaultSubject,

    // Add client metadata in a formatted way
    clientInfo: `...`,
    performanceInfo: `...`,
    behaviorInfo: `...`,
    engagementInfo: `...`,
    conversionInfo: `...`,
    marketingInfo: `...`
  })
});
```

## Norwegian Date Formatting

The component includes a utility function to format dates in Norwegian format:

```typescript
const formatNorwegianDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  
  return `${year}.${month}.${day}, Kl.${hours}:${minutes}`;
};
```

## Technical Considerations

1. **Performance Impact**: The analytics collection is designed to have minimal impact on page performance
2. **Error Handling**: All data collection methods include error handling to prevent crashes
3. **Fallbacks**: Default values are provided for all data points in case collection fails
4. **Privacy**: Only collects data when the form is submitted

## Related Documentation

For more comprehensive documentation, see:

1. [Contact Form Analytics Documentation](../../docs/CONTACT_FORM_ANALYTICS.md)
2. [Contact Form README](../sections/60-contact/README.md)
