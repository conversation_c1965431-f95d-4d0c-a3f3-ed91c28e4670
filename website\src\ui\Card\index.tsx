import React from 'react';
import { cn  } from '@/lib/utils';
import { Link, To } from 'react-router-dom';
import { encodeImagePath  } from '@/lib/utils/paths';

interface CardProps {
  title: string;
  description?: string;
  image?: string;
  link?: string;
  className?: string;
  children?: React.ReactNode;
  onClick?: () => void;
  aspectRatio?: 'square' | 'video' | 'portrait';
  variant?: 'default' | 'hover' | 'interactive';
}

const Card: React.FC<CardProps> = ({
  title,
  description,
  image,
  link,
  className,
  children,
  onClick,
  aspectRatio = 'square',
  variant = 'default'
}) => {
  const commonClassNames = cn(
    'group relative bg-white rounded-lg overflow-hidden transition-all duration-300',
    variant === 'hover' && 'hover:shadow-lg',
    variant === 'interactive' && 'cursor-pointer hover:-translate-y-1 hover:shadow-lg',
    className
  );

  // Render as Link if link is provided
  if (link) {
    return (
      <Link
        to={link as To}
        className={commonClassNames}
      >
        {renderCardContent()}
      </Link>
    );
  }

  // Render as button if onClick is provided
  if (onClick) {
    return (
      <button
        onClick={onClick}
        className={commonClassNames}
      >
        {renderCardContent()}
      </button>
    );
  }

  // Otherwise render as div
  return (
    <div className={commonClassNames}>
      {renderCardContent()}
    </div>
  );

  // Helper function to render card content
  function renderCardContent() {
    return (
      <>

        {image && (
          <div className={cn(
            'relative overflow-hidden bg-gray-100',
            aspectRatio === 'square' && 'aspect-square',
            aspectRatio === 'video' && 'aspect-video',
            aspectRatio === 'portrait' && 'aspect-[3/4]'
          )}>
            <img
              src={encodeImagePath(image)}
              alt={title}
              className="absolute inset-0 w-full h-full object-cover"
            />
          </div>
        )}
        <div className="p-4 sm:p-6">
          <h3 className="text-lg font-semibold mb-2">{title}</h3>
          {description && (
            <p className="text-gray-600 text-sm line-clamp-2 mb-4">{description}</p>
          )}
          {children}
        </div>
      </>
    );
  }
};

export { Card };
export default Card;