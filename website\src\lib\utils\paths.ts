/**
 * Path utility functions
 */

/**
 * Encode special characters in image paths to ensure they load correctly
 * This is especially important for paths with Norwegian characters like æ, ø, å
 * and for paths with geocoordinates
 *
 * @param path The image path to encode
 * @returns The encoded image path
 */
export const encodeImagePath = (path: string): string => {
  if (!path) return '';

  // Split the path into segments
  const segments = path.split('/');

  // Encode each segment and join them back together
  const encodedPath = segments
    .map(segment => {
      // Don't encode empty segments
      if (!segment) {
        return segment;
      }

      // Check if this segment contains special characters or geocoordinates
      const hasSpecialChars = /[æøåÆØÅ]/.test(segment);
      const hasGeoCoordinates = /_\d+\.\d+_\d+\.\d+\./.test(segment);

      if (hasSpecialChars || hasGeoCoordinates) {
        return encodeURIComponent(segment);
      }

      return segment;
    })
    .join('/');

  return encodedPath;
};
