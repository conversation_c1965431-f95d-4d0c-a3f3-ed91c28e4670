/**
 * SEO Constants Module
 *
 * This module centralizes all SEO-related constants and utilities,
 * with a focus on phrase-based keywords rather than single keywords.
 *
 * It provides:
 * 1. Structured keyword phrases with variations and subphrases
 * 2. Page-specific SEO data (title, description, keywords, schema)
 * 3. Utility functions for generating SEO metadata
 */

import { CONTACT_INFO, getCompanySchema } from './contact';
import { SERVICE_AREAS } from './locations';

// Define SEO data interface
export interface SEOData {
  title: string;
  description: string;
  keywords: string[];
  schema: any;
  segment?: string;
  faqSchema?: any;
}

/**
 * SEO Phrase Types
 */
export type KeywordPhrase = {
  phrase: string;           // Complete phrase
  subphrases?: string[];    // Component parts of the phrase
  variations?: string[];    // Semantic variations
  priority: number;         // 1-10 scale (10 being highest)
  intent: 'informational' | 'navigational' | 'transactional' | 'commercial';
};

/**
 * Core SEO phrases organized by category
 */
export const CORE_PHRASES: Record<string, KeywordPhrase[]> = {
  services: [
    {
      phrase: "anleggsgartner for skrånende tomt",
      subphrases: ["anleggsgartner", "skrånende tomt", "hage i skråning"],
      variations: ["anleggsgartner bratt tomt", "terrassering av hage"],
      priority: 8,
      intent: "commercial"
    },
    {
      phrase: "støttemur pris per meter",
      subphrases: ["støttemur", "pris støttemur", "kostnad støttemur"],
      variations: ["hva koster støttemur", "bygge støttemur pris"],
      priority: 9,
      intent: "commercial"
    },
    {
      phrase: "legge belegningsstein på gårdsplassen",
      subphrases: ["belegningsstein", "gårdsplass", "innkjørsel"],
      variations: ["belegningsstein innkjørsel", "stein på gårdsplass"],
      priority: 8,
      intent: "transactional"
    },
    {
      phrase: "anlegge ny plen",
      subphrases: ["ferdigplen", "så plen", "ny plen"],
      variations: ["legge ferdigplen", "anlegge plen"],
      priority: 7,
      intent: "transactional"
    },
    {
      phrase: "cortenstål i hagen",
      subphrases: ["cortenstål", "cortenstål hage", "rustent stål"],
      variations: ["cortenstål design", "rustfritt stål i hagen"],
      priority: 6,
      intent: "informational"
    }
  ],
  problems: [
    {
      phrase: "hvordan fikse dårlig drenering i hagen",
      subphrases: ["dårlig drenering", "vannproblemer hage", "fuktig hage"],
      variations: ["vann samler seg i hagen", "hagen blir gjørmete"],
      priority: 7,
      intent: "informational"
    },
    {
      phrase: "hjelp med utglidning av skråning",
      subphrases: ["utglidning", "erosjon", "skråning"],
      variations: ["jorda sklir ut i skråning", "hindre erosjon i skråning"],
      priority: 6,
      intent: "commercial"
    },
    {
      phrase: "løsning for bratt hage",
      subphrases: ["bratt hage", "skrånende tomt", "terrassering"],
      variations: ["utnytte bratt tomt", "nivåforskjeller i hagen"],
      priority: 8,
      intent: "commercial"
    }
  ],
  seasonal: [
    {
      phrase: "klargjøre hagen for vinteren",
      subphrases: ["vinterhage", "forberede hage", "høstarbeid"],
      variations: ["hagearbeid før vinteren", "beskytte hagen mot frost"],
      priority: 8,
      intent: "informational"
    },
    {
      phrase: "anlegge ny plen om våren",
      subphrases: ["ny plen", "så plen", "ferdigplen"],
      variations: ["beste tid for ny plen", "legge ferdigplen vår"],
      priority: 9,
      intent: "transactional"
    },
    {
      phrase: "vedlikehold av belegningsstein",
      subphrases: ["vedlikehold belegningsstein", "rengjøre belegningsstein"],
      variations: ["fjerne ugress mellom belegningsstein", "høytrykkspyle belegningsstein"],
      priority: 7,
      intent: "informational"
    }
  ]
};

/**
 * Location-specific phrases
 */
export const LOCATION_PHRASES: Record<string, KeywordPhrase[]> = {
  ringerike: [
    {
      phrase: "anleggsgartner i Ringerike med erfaring",
      subphrases: ["anleggsgartner Ringerike", "erfaren anleggsgartner"],
      variations: ["profesjonell anleggsgartner Ringerike", "dyktig anleggsgartner nær meg"],
      priority: 10,
      intent: "navigational"
    },
    {
      phrase: "hagedesign tilpasset Ringerikes klima",
      subphrases: ["hagedesign Ringerike", "klimatilpasset hage"],
      variations: ["hage for norsk klima", "lokaltilpasset hagedesign"],
      priority: 7,
      intent: "informational"
    }
  ],
  hole: [
    {
      phrase: "anleggsgartner i Hole kommune",
      subphrases: ["anleggsgartner Hole", "hagearbeid Hole"],
      variations: ["anleggsgartner nær Hole", "lokal anleggsgartner Hole"],
      priority: 10,
      intent: "navigational"
    },
    {
      phrase: "støttemur for skrånende tomt i Hole",
      subphrases: ["støttemur Hole", "skrånende tomt"],
      variations: ["bygge støttemur i Hole", "terrassering Hole"],
      priority: 8,
      intent: "commercial"
    }
  ],
  honefoss: [
    {
      phrase: "belegningsstein til innkjørsel i Hønefoss",
      subphrases: ["belegningsstein Hønefoss", "innkjørsel stein"],
      variations: ["legge stein innkjørsel Hønefoss", "oppgradere innkjørsel"],
      priority: 9,
      intent: "transactional"
    }
  ],
  royse: [
    {
      phrase: "anleggsgartner på Røyse",
      subphrases: ["anleggsgartner Røyse", "hagearbeid Røyse"],
      variations: ["lokal anleggsgartner Røyse", "hagedesign Røyse"],
      priority: 10,
      intent: "navigational"
    }
  ],
  jevnaker: [
    {
      phrase: "anleggsgartner i Jevnaker",
      subphrases: ["anleggsgartner Jevnaker", "hagearbeid Jevnaker"],
      variations: ["lokal anleggsgartner Jevnaker", "hagedesign Jevnaker"],
      priority: 9,
      intent: "navigational"
    }
  ],
  sundvollen: [
    {
      phrase: "anleggsgartner i Sundvollen",
      subphrases: ["anleggsgartner Sundvollen", "hagearbeid Sundvollen"],
      variations: ["lokal anleggsgartner Sundvollen", "hagedesign Sundvollen"],
      priority: 9,
      intent: "navigational"
    }
  ],
  vik: [
    {
      phrase: "anleggsgartner i Vik",
      subphrases: ["anleggsgartner Vik", "hagearbeid Vik"],
      variations: ["lokal anleggsgartner Vik", "hagedesign Vik"],
      priority: 9,
      intent: "navigational"
    }
  ]
};

/**
 * Question-based phrases (for featured snippets and voice search)
 */
export const QUESTION_PHRASES: KeywordPhrase[] = [
  {
    phrase: "hva koster en støttemur per meter?",
    subphrases: ["støttemur pris", "kostnad støttemur"],
    variations: ["pris på å bygge støttemur", "hvor mye koster støttemur"],
    priority: 8,
    intent: "commercial"
  },
  {
    phrase: "når bør jeg anlegge ny plen i Ringerike?",
    subphrases: ["anlegge plen", "så plen", "beste tid plen"],
    variations: ["beste tidspunkt for plen", "så gress Ringerike"],
    priority: 7,
    intent: "informational"
  },
  {
    phrase: "hvordan velge riktig belegningsstein til innkjørselen?",
    subphrases: ["velge belegningsstein", "belegningsstein innkjørsel"],
    variations: ["best belegningsstein", "type stein til innkjørsel"],
    priority: 6,
    intent: "informational"
  }
];

/**
 * Generate meta keywords from phrases
 *
 * @param categories - Categories of phrases to include
 * @param location - Optional location to include location-specific phrases
 * @returns Array of keyword phrases
 */
export function generateKeywords(
  categories: string[],
  location?: string
): string[] {
  let result: string[] = [];

  // Add core phrases from selected categories
  categories.forEach(category => {
    if (CORE_PHRASES[category]) {
      CORE_PHRASES[category]
        .sort((a, b) => b.priority - a.priority)
        .slice(0, 5) // Top 5 phrases by priority
        .forEach(keywordPhrase => {
          result.push(keywordPhrase.phrase);
          // Add some subphrases
          if (keywordPhrase.subphrases) {
            result = result.concat(keywordPhrase.subphrases.slice(0, 2));
          }
        });
    }
  });

  // Add location-specific phrases if location is provided
  if (location && LOCATION_PHRASES[location]) {
    LOCATION_PHRASES[location]
      .sort((a, b) => b.priority - a.priority)
      .slice(0, 3) // Top 3 location phrases
      .forEach(keywordPhrase => {
        result.push(keywordPhrase.phrase);
        // Add some subphrases
        if (keywordPhrase.subphrases) {
          result = result.concat(keywordPhrase.subphrases);
        }
      });
  }

  // Add a couple of question phrases for voice search optimization
  result = result.concat(
    QUESTION_PHRASES
      .sort((a, b) => b.priority - a.priority)
      .slice(0, 2)
      .map(q => q.phrase)
  );

  // Remove duplicates and limit to reasonable number
  return [...new Set(result)].slice(0, 20);
}

/**
 * Generate schema.org FAQ section from question phrases
 *
 * @param categories - Categories to filter questions by
 * @returns FAQ schema object
 */
export function generateFAQSchema(categories: string[]): any {
  const relevantQuestions = QUESTION_PHRASES
    .filter(q => categories.some(cat =>
      CORE_PHRASES[cat]?.some(phrase =>
        phrase.subphrases?.some(sub =>
          q.phrase.includes(sub)
        )
      )
    ))
    .sort((a, b) => b.priority - a.priority)
    .slice(0, 5);

  // Generate answers would come from a separate content module
  // This is simplified for the example
  return {
    "@type": "FAQPage",
    "mainEntity": relevantQuestions.map(q => ({
      "@type": "Question",
      "name": q.phrase,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": `[Answer would be generated based on the question: ${q.phrase}]`
      }
    }))
  };
}

/**
 * Page-specific SEO data
 */
export const PAGE_SEO = {
  home: {
    title: `Anleggsgartner i Ringerike | ${CONTACT_INFO.company.name}`,
    description: CONTACT_INFO.company.description,
    keywords: generateKeywords(['services', 'problems'], 'ringerike'),
    keywordCategories: ['services', 'problems'],
    locationId: 'ringerike',
    schema: getCompanySchema(),
    h1: `Anleggsgartner i Ringerike med fokus på kvalitet og holdbarhet`,
    h2Phrases: [
      "Profesjonell anleggsgartner for din hage og uterom",
      "Spesialister på støttemurer og belegningsstein",
      "Lokalkjente anleggsgartnere i Ringerike-regionen"
    ]
  },
  services: {
    title: `Anleggsgartnertjenester i Ringerike | ${CONTACT_INFO.company.name}`,
    description: `Vi tilbyr alt fra legging av ferdigplen til bygging av støttemurer, tilpasset både private hager og næringseiendommer i Ringerike-området.`,
    keywords: generateKeywords(['services', 'seasonal'], 'ringerike'),
    keywordCategories: ['services', 'seasonal'],
    locationId: 'ringerike',
    schema: {
      "@type": "Service",
      "name": "Anleggsgartnertjenester",
      "provider": getCompanySchema(),
      "areaServed": "Ringerike"
    },
    faqSchema: generateFAQSchema(['services']),
    h1: `Komplette anleggsgartnertjenester i Ringerike`,
    h2Phrases: [
      "Støttemurer tilpasset lokale grunnforhold",
      "Belegningsstein for innkjørsler og terrasser",
      "Ferdigplen og hagedesign for nordisk klima"
    ]
  },
  about: {
    title: `Om oss | ${CONTACT_INFO.company.name}`,
    description: `${CONTACT_INFO.company.name} er din lokale anleggsgartner i Ringerike-regionen. Vi har lang erfaring med å skape varige uterom tilpasset lokale forhold.`,
    keywords: generateKeywords(['services'], 'ringerike'),
    keywordCategories: ['services'],
    locationId: 'ringerike',
    schema: getCompanySchema(),
    h1: `Om ${CONTACT_INFO.company.name}`,
    h2Phrases: [
      "Vår historie og erfaring",
      "Vårt team av fagfolk",
      "Vår tilnærming til hagedesign"
    ]
  },
  projects: {
    title: `Prosjekter | ${CONTACT_INFO.company.name}`,
    description: `Se våre gjennomførte prosjekter i Ringerike-området. Fra små hageprosjekter til større anlegg for bedrifter og offentlige kunder.`,
    keywords: generateKeywords(['services'], 'ringerike'),
    keywordCategories: ['services'],
    locationId: 'ringerike',
    schema: getCompanySchema(),
    h1: `Våre prosjekter`,
    h2Phrases: [
      "Ferdigstilte hageprosjekter",
      "Støttemurer og terrasseringer",
      "Belegningsstein og innkjørsler"
    ]
  },
  contact: {
    title: `Kontakt oss | ${CONTACT_INFO.company.name}`,
    description: `Ta kontakt med ${CONTACT_INFO.company.name} for en uforpliktende befaring eller tilbud på ditt prosjekt. Vi er din lokale anleggsgartner i Ringerike-regionen.`,
    keywords: generateKeywords(['services'], 'ringerike'),
    keywordCategories: ['services'],
    locationId: 'ringerike',
    schema: getCompanySchema(),
    h1: `Kontakt oss`,
    h2Phrases: [
      "Ta kontakt for en uforpliktende befaring",
      "Våre kontaktopplysninger",
      "Finn oss i Ringerike"
    ]
  }
};

/**
 * Get SEO data for a specific page
 *
 * @param pageName - Name of the page to get SEO data for
 * @param customData - Optional custom data to override defaults
 * @returns SEO data for the page
 */
export function getPageSeo(
  pageName: keyof typeof PAGE_SEO,
  customData?: {
    title?: string;
    description?: string;
    locationId?: string;
  }
): SEOData {
  const baseSeo = PAGE_SEO[pageName];
  const locationId = customData?.locationId || baseSeo.locationId;

  // Generate location-specific keywords
  const keywords = generateKeywords(
    baseSeo.keywordCategories,
    locationId
  );

  // Get location-specific schema if needed
  let schema = locationId && locationId !== baseSeo.locationId
    ? getCompanySchema(locationId)
    : baseSeo.schema;

  // Use custom title or base title
  let title = customData?.title || baseSeo.title;

  // Use custom description or base description
  let description = customData?.description || baseSeo.description;

  // Create the result object with required properties
  const result: SEOData = {
    title,
    description,
    keywords,
    schema,
    segment: 'general'
  };

  // Add faqSchema only if it exists in baseSeo
  if ('faqSchema' in baseSeo) {
    result.faqSchema = baseSeo.faqSchema;
  }

  return result;
}

/**
 * Get SEO data for a location page
 *
 * @param locationId - ID of the location
 * @returns SEO data for the location page
 */
export function getLocationSeo(locationId: string): SEOData {
  // SERVICE_AREAS is an object, not an array, so we need to access it directly
  const serviceArea = SERVICE_AREAS[locationId as keyof typeof SERVICE_AREAS];

  if (!serviceArea) {
    return getPageSeo('home');
  }

  // Since the structure is different, adjust how we access properties
  return getPageSeo('services', {
    title: `Anleggsgartner i ${serviceArea.name} | ${CONTACT_INFO.company.name}`,
    description: `Vi er din lokale anleggsgartner i ${serviceArea.name}. Vi tilbyr tjenester som ${serviceArea.popularServices.join(', ')} tilpasset lokale forhold.`,
    locationId
  });
}


