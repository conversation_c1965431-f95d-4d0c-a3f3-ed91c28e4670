/**
 * PageSEO Component
 *
 * A reusable component for applying SEO metadata to pages.
 * Uses the centralized SEO module to ensure consistency.
 */

import { getPageSeo, getLocationSeo } from '@/lib/constants/seo';
import { Meta } from '@/layout/Meta';

interface PageSEOProps {
  /**
   * Name of the page to get SEO data for
   */
  pageName: 'home' | 'services' | 'about' | 'projects' | 'contact';

  /**
   * Optional location ID for location-specific pages
   */
  locationId?: string;

  /**
   * Optional custom title to override the default
   */
  customTitle?: string;

  /**
   * Optional custom description to override the default
   */
  customDescription?: string;

  /**
   * Optional custom image to override the default
   */
  customImage?: string;
}

/**
 * PageSEO Component
 *
 * Applies SEO metadata to a page using the centralized SEO module.
 */
export const PageSEO = ({
  pageName,
  locationId,
  customTitle,
  customDescription,
  customImage
}: PageSEOProps) => {
  // Get SEO data from the centralized module based on the provided parameters
  let seoData;

  if (locationId) {
    // Location specified
    seoData = getLocationSeo(locationId);
  } else {
    // No location specified
    seoData = getPageSeo(pageName, {
      title: customTitle,
      description: customDescription
    });
  }

  // Prepare schema.org data
  const schemas = [seoData.schema];

  // Add FAQ schema if available
  if (seoData.faqSchema) {
    schemas.push(seoData.faqSchema);
  }

  // Combine schemas
  const schemaData = { '@context': 'https://schema.org', '@graph': schemas };

  return (
    <Meta
      title={seoData.title}
      description={seoData.description}
      keywords={seoData.keywords}
      schema={schemaData}
      image={customImage}
    />
  );
};

export default PageSEO;
