import React from 'react';
import { X, AlertCircle, CheckCircle, Info } from 'lucide-react';
import { useApp } from '../lib/context/AppContext';
import { cn  } from '@/lib/utils';

const Notifications: React.FC = () => {
  const { state, removeNotification } = useApp();
  const { notifications } = state;

  if (notifications.length === 0) return null;

  return (
    <div className="fixed top-4 right-4 z-50 flex flex-col gap-2 max-w-md">
      {notifications.map(notification => (
        <div
          key={notification.id}
          className={cn(
            'p-4 rounded-lg shadow-lg flex items-start gap-3 animate-fade-in',
            notification.type === 'error' && 'bg-red-50 text-red-800 border border-red-200',
            notification.type === 'success' && 'bg-green-50 text-green-800 border border-green-200',
            notification.type === 'info' && 'bg-blue-50 text-blue-800 border border-blue-200'
          )}
        >
          <div className="flex-shrink-0">
            {notification.type === 'error' && <AlertCircle className="w-5 h-5 text-red-500" />}
            {notification.type === 'success' && <CheckCircle className="w-5 h-5 text-green-500" />}
            {notification.type === 'info' && <Info className="w-5 h-5 text-blue-500" />}
          </div>
          <div className="flex-1">
            <p className="text-sm">{notification.message}</p>
          </div>
          <button
            onClick={() => removeNotification(notification.id)}
            className="flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors"
            aria-label="Close notification"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      ))}
    </div>
  );
};

export { Notifications };
export default Notifications;