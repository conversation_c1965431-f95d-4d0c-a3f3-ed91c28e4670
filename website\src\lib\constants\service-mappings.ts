/**
 * Service Mappings
 *
 * This file provides a consolidated mapping system for services, categories, features, and seasons.
 * It serves as a single source of truth for service relationships throughout the application.
 */

import { SeasonType } from '@/lib/types/content';

/**
 * Service category definitions with display order and service references
 */
export const SERVICE_CATEGORIES = {
  // Outdoor elements - Order matches the spring season order
  FERDIGPLEN: {
    id: 'ferdigplen',
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    order: 10, // Spring order: 1
    serviceId: 'ferdigplen'
  },
  HEKK_OG_BEPLANTNING: {
    id: 'hekk',
    name: 'Hekk og Beplantning',
    order: 20, // Spring order: 2
    serviceId: 'hekk'
  },
  BELEGNINGSSTEIN: {
    id: 'belegning<PERSON>tein',
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    order: 30, // Spring order: 3
    serviceId: 'belegningsstein'
  },
  STOTTEMURER: {
    id: 'stottemurer',
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    order: 40, // Spring order: 4
    serviceId: 'stottemurer'
  },
  KANTSTEIN: {
    id: 'kants<PERSON>',
    name: '<PERSON><PERSON><PERSON>',
    order: 50, // Spring order: 5
    serviceId: 'kantstein'
  },
  CORTENSTAAL: {
    id: 'cortenstaal',
    name: '<PERSON>rtenstål',
    order: 60, // Spring order: 6
    serviceId: 'cortenstaal'
  },
  TRAPPER_OG_REPOER: {
    id: 'trapper',
    name: 'Trapper og Repoer',
    order: 70, // Spring order: 7
    serviceId: 'trapp-repo'
  },
  PLATTING: {
    id: 'platting',
    name: 'Platting',
    order: 80, // Spring order: 8
    serviceId: 'platting'
  },
  PLANLEGGING_OG_DESIGN: {
    id: 'planlegging',
    name: 'Planlegging og Design',
    order: 90, // Spring order: 9
    serviceId: 'planlegging'
  }
} as const;

/**
 * Service feature definitions with display order
 */
export const SERVICE_FEATURES = {
  // Garden maintenance
  PLANTING: { id: 'planting', name: 'Planting', order: 10 },
  VANNING: { id: 'vanning', name: 'Vanning', order: 20 },
  GJODSLING: { id: 'gjodsling', name: 'Gjødsling', order: 30 },
  JORDARBEID: { id: 'jordarbeid', name: 'Jordarbeid', order: 40 },
  BESKJAERING: { id: 'beskjaering', name: 'Beskjæring', order: 50 },
  // Construction
  ANLEGG: { id: 'anlegg', name: 'Anlegg', order: 60 },
  TERRASSE: { id: 'terrasse', name: 'Terrasse', order: 70 },
  // Maintenance
  VEDLIKEHOLD: { id: 'vedlikehold', name: 'Vedlikehold', order: 80 },
  DRENERING: { id: 'drenering', name: 'Drenering', order: 90 },
  VINTERKLARGJORING: { id: 'vinterklargjoring', name: 'Vinterklargjøring', order: 100 },
  // Planning
  PLANLEGGING: { id: 'planlegging', name: 'Planlegging', order: 110 },
  DESIGN: { id: 'design', name: 'Design', order: 120 },
  PROSJEKTERING: { id: 'prosjektering', name: 'Prosjektering', order: 130 }
} as const;

/**
 * Season definitions with display order
 */
export const SEASONS = {
  VAR: { id: 'vår', name: 'Vår', displayName: 'Våren', englishName: 'spring', order: 10 },
  SOMMER: { id: 'sommer', name: 'Sommer', displayName: 'Sommeren', englishName: 'summer', order: 20 },
  HOST: { id: 'høst', name: 'Høst', displayName: 'Høsten', englishName: 'fall', order: 30 },
  VINTER: { id: 'vinter', name: 'Vinter', displayName: 'Vinteren', englishName: 'winter', order: 40 }
} as const;

/**
 * Forward mappings - the single source of truth for relationships
 */

// Season to Category mappings with explicit order
export const seasonToCategoryIds: Record<SeasonType, string[]> = {
  // Spring - Most services are available
  'vår': [
    SERVICE_CATEGORIES.FERDIGPLEN.id,              // order: 1
    SERVICE_CATEGORIES.HEKK_OG_BEPLANTNING.id,     // order: 2
    SERVICE_CATEGORIES.BELEGNINGSSTEIN.id,         // order: 3
    SERVICE_CATEGORIES.STOTTEMURER.id,             // order: 4
    SERVICE_CATEGORIES.KANTSTEIN.id,               // order: 5
    SERVICE_CATEGORIES.CORTENSTAAL.id,             // order: 6
    SERVICE_CATEGORIES.TRAPPER_OG_REPOER.id,       // order: 7
    SERVICE_CATEGORIES.PLATTING.id,                // order: 8
  ],
  // Summer - Most services are available
  'sommer': [
    SERVICE_CATEGORIES.FERDIGPLEN.id,              // order: 1
    SERVICE_CATEGORIES.HEKK_OG_BEPLANTNING.id,     // order: 2
    SERVICE_CATEGORIES.BELEGNINGSSTEIN.id,         // order: 3
    SERVICE_CATEGORIES.STOTTEMURER.id,             // order: 4
    SERVICE_CATEGORIES.KANTSTEIN.id,               // order: 5
    SERVICE_CATEGORIES.CORTENSTAAL.id,             // order: 6
    SERVICE_CATEGORIES.PLATTING.id,                // order: 7
    SERVICE_CATEGORIES.TRAPPER_OG_REPOER.id,       // order: 8
  ],
  // Fall - Some services are available
  'høst': [
    SERVICE_CATEGORIES.FERDIGPLEN.id,              // order: 1
    SERVICE_CATEGORIES.BELEGNINGSSTEIN.id,         // order: 2
    SERVICE_CATEGORIES.STOTTEMURER.id,             // order: 3
    SERVICE_CATEGORIES.KANTSTEIN.id,               // order: 4
    SERVICE_CATEGORIES.PLATTING.id,                // order: 5
    SERVICE_CATEGORIES.CORTENSTAAL.id,             // order: 6
    SERVICE_CATEGORIES.TRAPPER_OG_REPOER.id,       // order: 7
    SERVICE_CATEGORIES.PLANLEGGING_OG_DESIGN.id    // order: 8
  ],
  // Winter - Limited services are available
  'vinter': [
    SERVICE_CATEGORIES.BELEGNINGSSTEIN.id,         // order: 1
    SERVICE_CATEGORIES.STOTTEMURER.id,             // order: 2
    SERVICE_CATEGORIES.KANTSTEIN.id,               // order: 3
    SERVICE_CATEGORIES.PLANLEGGING_OG_DESIGN.id    // order: 4
  ]
} as const;

// Season to Feature mappings with explicit order
export const seasonToFeatureIds: Record<SeasonType, string[]> = {
  // Spring features
  'vår': [
    SERVICE_FEATURES.PLANTING.id,                  // order: 1
    SERVICE_FEATURES.VANNING.id,                   // order: 2
    SERVICE_FEATURES.GJODSLING.id,                 // order: 3
    SERVICE_FEATURES.JORDARBEID.id,                // order: 4
    SERVICE_FEATURES.ANLEGG.id,                    // order: 5
    SERVICE_FEATURES.TERRASSE.id,                  // order: 6
    SERVICE_FEATURES.VEDLIKEHOLD.id,               // order: 7
    SERVICE_FEATURES.PLANLEGGING.id,               // order: 8
    SERVICE_FEATURES.DESIGN.id,                    // order: 9
    SERVICE_FEATURES.PROSJEKTERING.id              // order: 10
  ],
  // Summer features
  'sommer': [
    SERVICE_FEATURES.PLANTING.id,                  // order: 1
    SERVICE_FEATURES.VANNING.id,                   // order: 2
    SERVICE_FEATURES.GJODSLING.id,                 // order: 3
    SERVICE_FEATURES.JORDARBEID.id,                // order: 4
    SERVICE_FEATURES.ANLEGG.id,                    // order: 5
    SERVICE_FEATURES.TERRASSE.id,                  // order: 6
    SERVICE_FEATURES.VEDLIKEHOLD.id,               // order: 7
    SERVICE_FEATURES.PLANLEGGING.id,               // order: 8
    SERVICE_FEATURES.DESIGN.id,                    // order: 9
    SERVICE_FEATURES.PROSJEKTERING.id              // order: 10
  ],
  // Fall features
  'høst': [
    SERVICE_FEATURES.PLANTING.id,                  // order: 1
    SERVICE_FEATURES.JORDARBEID.id,                // order: 2
    SERVICE_FEATURES.ANLEGG.id,                    // order: 3
    SERVICE_FEATURES.TERRASSE.id,                  // order: 4
    SERVICE_FEATURES.BESKJAERING.id,               // order: 5
    SERVICE_FEATURES.DRENERING.id,                 // order: 6
    SERVICE_FEATURES.VINTERKLARGJORING.id,         // order: 7
    SERVICE_FEATURES.PLANLEGGING.id,               // order: 8
    SERVICE_FEATURES.DESIGN.id,                    // order: 9
    SERVICE_FEATURES.PROSJEKTERING.id              // order: 10
  ],
  // Winter features
  'vinter': [
    SERVICE_FEATURES.PLANLEGGING.id,               // order: 1
    SERVICE_FEATURES.DESIGN.id,                    // order: 2
    SERVICE_FEATURES.PROSJEKTERING.id,             // order: 3
    SERVICE_FEATURES.ANLEGG.id                     // order: 4
  ]
} as const;

// Category to Feature mappings
export const categoryToFeatureIds: Record<string, string[]> = {
  // Ferdigplen features
  'ferdigplen': [
    SERVICE_FEATURES.PLANTING.id,
    SERVICE_FEATURES.VANNING.id,
    SERVICE_FEATURES.GJODSLING.id,
    SERVICE_FEATURES.JORDARBEID.id,
    SERVICE_FEATURES.VEDLIKEHOLD.id
  ],
  // Hekk og Beplantning features
  'hekk': [
    SERVICE_FEATURES.PLANTING.id,
    SERVICE_FEATURES.VANNING.id,
    SERVICE_FEATURES.GJODSLING.id,
    SERVICE_FEATURES.JORDARBEID.id,
    SERVICE_FEATURES.BESKJAERING.id,
    SERVICE_FEATURES.VEDLIKEHOLD.id
  ],
  // Platting features
  'platting': [
    SERVICE_FEATURES.ANLEGG.id,
    SERVICE_FEATURES.TERRASSE.id,
    SERVICE_FEATURES.VEDLIKEHOLD.id
  ],
  // Cortenstål features
  'cortenstaal': [
    SERVICE_FEATURES.ANLEGG.id,
    SERVICE_FEATURES.JORDARBEID.id
  ],
  // Belegningsstein features
  'belegningsstein': [
    SERVICE_FEATURES.ANLEGG.id,
    SERVICE_FEATURES.JORDARBEID.id,
    SERVICE_FEATURES.VEDLIKEHOLD.id
  ],
  // Støttemurer features
  'stottemurer': [
    SERVICE_FEATURES.ANLEGG.id,
    SERVICE_FEATURES.JORDARBEID.id,
    SERVICE_FEATURES.DRENERING.id
  ],
  // Kantstein features
  'kantstein': [
    SERVICE_FEATURES.ANLEGG.id,
    SERVICE_FEATURES.JORDARBEID.id
  ],
  // Trapper og Repoer features
  'trapper': [
    SERVICE_FEATURES.ANLEGG.id,
    SERVICE_FEATURES.JORDARBEID.id
  ],
  // Planlegging og Design features
  'planlegging': [
    SERVICE_FEATURES.PLANLEGGING.id,
    SERVICE_FEATURES.DESIGN.id,
    SERVICE_FEATURES.PROSJEKTERING.id
  ]
} as const;

/**
 * Utility function to build reverse mappings
 * Creates the inverse relationship from a forward mapping
 */
function buildReverseMapping<K extends string, V extends string>(
  forward: Record<K, V[]>
): Record<string, string[]> {
  const reversed: Record<string, string[]> = {};

  Object.entries(forward).forEach(([key, values]) => {
    (values as string[]).forEach((value: string) => {
      if (!reversed[value]) {
        reversed[value] = [];
      }
      reversed[value].push(key);
    });
  });

  return reversed;
}

// Generate reverse mappings automatically
export const categoryToSeasonIds = buildReverseMapping(seasonToCategoryIds);
export const featureToCategoryIds = buildReverseMapping(categoryToFeatureIds);
export const featureToSeasonIds = buildReverseMapping(seasonToFeatureIds);

/**
 * Generic utility function to map IDs to names
 */
type IdDefinition = { id: string; name: string; order?: number };

function mapIdsToNames<T extends IdDefinition>(
  allDefinitions: Record<string, T>,
  ids: string[]
): string[] {
  return ids.map(id => {
    const entry = Object.values(allDefinitions).find(def => def.id === id);
    return entry ? entry.name : id;
  });
}

// Specialized mapping functions for convenience
export function mapCategoryIdsToNames(ids: string[]): string[] {
  return mapIdsToNames(SERVICE_CATEGORIES, ids);
}

export function mapFeatureIdsToNames(ids: string[]): string[] {
  return mapIdsToNames(SERVICE_FEATURES, ids);
}

/**
 * Utility function to normalize a string to an ID
 */
function normalizeToId<T extends IdDefinition>(
  allDefinitions: Record<string, T>,
  nameOrId: string
): string {
  const entry = Object.values(allDefinitions).find(
    def => def.name === nameOrId || def.id === nameOrId
  );
  return entry?.id || nameOrId;
}

// Specialized normalization functions for convenience
export function normalizeCategoryToId(category: string): string {
  return normalizeToId(SERVICE_CATEGORIES, category);
}

export function normalizeFeatureToId(feature: string): string {
  return normalizeToId(SERVICE_FEATURES, feature);
}

/**
 * Service ID to category mapping (for backward compatibility)
 */
export const SERVICE_ID_TO_CATEGORY = Object.values(SERVICE_CATEGORIES).reduce(
  (acc, category) => {
    acc[category.serviceId] = category.name;
    return acc;
  },
  {} as Record<string, string>
);

/**
 * For backward compatibility
 */
export const SEASON_TO_SERVICE_CATEGORIES: Record<SeasonType, string[]> = {
  'vår': mapCategoryIdsToNames(seasonToCategoryIds['vår']),
  'sommer': mapCategoryIdsToNames(seasonToCategoryIds['sommer']),
  'høst': mapCategoryIdsToNames(seasonToCategoryIds['høst']),
  'vinter': mapCategoryIdsToNames(seasonToCategoryIds['vinter'])
};

/**
 * For backward compatibility
 */
export const SEASON_TO_SERVICE_FEATURES: Record<SeasonType, string[]> = {
  'vår': mapFeatureIdsToNames(seasonToFeatureIds['vår']),
  'sommer': mapFeatureIdsToNames(seasonToFeatureIds['sommer']),
  'høst': mapFeatureIdsToNames(seasonToFeatureIds['høst']),
  'vinter': mapFeatureIdsToNames(seasonToFeatureIds['vinter'])
};

/**
 * For backward compatibility
 */
export const SERVICE_CATEGORY_TO_SEASONS = categoryToSeasonIds;

/**
 * Comprehensive mapping structure that defines all relationships (for backward compatibility)
 */
export const SERVICE_MAPPINGS = {
  // Forward mappings
  seasonToCategories: seasonToCategoryIds,
  seasonToFeatures: seasonToFeatureIds,
  categoryToFeatures: categoryToFeatureIds,

  // Reverse mappings (now generated automatically)
  categoryToSeasons: categoryToSeasonIds,
  featureToCategories: featureToCategoryIds,
  featureToSeasons: featureToSeasonIds
};

// Helper functions

/**
 * Helper function to get all service categories for a season
 * @param season The season to get categories for
 * @returns Array of category names in the defined order
 */
export const getServiceCategoriesForSeason = (season: SeasonType): string[] => {
  const categoryIds = seasonToCategoryIds[season] || [];
  return mapCategoryIdsToNames(categoryIds);
};

/**
 * Helper function to get all service features for a season
 * @param season The season to get features for
 * @returns Array of feature names in the defined order
 */
export const getServiceFeaturesForSeason = (season: SeasonType): string[] => {
  const featureIds = seasonToFeatureIds[season] || [];
  return mapFeatureIdsToNames(featureIds);
};

/**
 * Helper function to get all seasons for a service category
 * @param category The category to get seasons for
 * @returns Array of season names
 */
export const getSeasonsForServiceCategory = (category: string): SeasonType[] => {
  const categoryId = normalizeCategoryToId(category);
  const seasons = categoryToSeasonIds[categoryId] || [];
  // Filter to ensure only valid SeasonType values are returned
  return seasons.filter((season): season is SeasonType =>
    ['vår', 'sommer', 'høst', 'vinter'].includes(season)
  );
};

/**
 * Helper function to get all features for a category
 * @param category The category to get features for
 * @returns Array of feature names in the defined order
 */
export const getFeaturesForCategory = (category: string): string[] => {
  const categoryId = normalizeCategoryToId(category);
  const featureIds = categoryToFeatureIds[categoryId] || [];
  return mapFeatureIdsToNames(featureIds);
};

/**
 * Helper function to get all categories for a feature
 * @param feature The feature to get categories for
 * @returns Array of category names in the defined order
 */
export const getCategoriesForFeature = (feature: string): string[] => {
  const featureId = normalizeFeatureToId(feature);
  const categoryIds = featureToCategoryIds[featureId] || [];
  return mapCategoryIdsToNames(categoryIds);
};

/**
 * Helper function to check if a category and feature are compatible
 * @param category The category to check
 * @param feature The feature to check
 * @returns Whether the category and feature are compatible
 */
export const areCategoryAndFeatureCompatible = (category: string, feature: string): boolean => {
  const featuresForCategory = getFeaturesForCategory(category);
  return featuresForCategory.includes(feature);
};

/**
 * Helper function to get the service ID for a category
 * @param category The category name or ID to get the service ID for
 * @returns The service ID for the category
 */
export const getServiceIdForCategory = (category: string): string | undefined => {
  const categoryEntry = Object.values(SERVICE_CATEGORIES).find(
    cat => cat.name === category || cat.id === category
  );
  return categoryEntry?.serviceId;
};

/**
 * Helper function to get all categories in order
 * @returns Array of category names sorted by the order property defined in SERVICE_CATEGORIES
 *
 * Note: The order property in SERVICE_CATEGORIES is set to match the spring season order.
 * If you need a different order for a specific season, use getServiceCategoriesForSeason(season)
 * which will use the order defined in the seasonToCategories arrays.
 */
export const getAllCategoriesInOrder = (): string[] => {
  return Object.values(SERVICE_CATEGORIES)
    .sort((a, b) => a.order - b.order)
    .map(cat => cat.name);
};

/**
 * Helper function to get all features in order
 * @returns Array of feature names sorted by order
 */
export const getAllFeaturesInOrder = (): string[] => {
  return Object.values(SERVICE_FEATURES)
    .sort((a, b) => a.order - b.order)
    .map(feat => feat.name);
};

/**
 * Helper function to get all seasons in order
 * @returns Array of season names sorted by order
 */
export const getAllSeasonsInOrder = (): SeasonType[] => {
  return Object.values(SEASONS)
    .sort((a, b) => a.order - b.order)
    .map(season => season.id as SeasonType);
};
