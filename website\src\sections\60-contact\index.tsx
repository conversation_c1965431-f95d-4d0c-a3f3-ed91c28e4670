import React, { useState, useEffect } from 'react';
import Container from '@/ui/Container';
import Hero from '@/ui/Hero';
import { CONTACT_INFO } from '@/lib/constants/contact';
import { Meta } from '@/layout/Meta';

// Helper function to format dates in Norwegian format (yyyy.MM.dd, Kl.HH:mm)
const formatNorwegianDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  return `${year}.${month}.${day}, Kl.${hours}:${minutes}`;
};

interface FormData {
  name: string;
  email: string;
  phone: string;
  address: string;
  description: string;
}

interface FormErrors {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  description?: string;
}

const ContactPage = () => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    address: '',
    description: ''
  });

  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitted, setIsSubmitted] = useState(false);

  // Define the client info type for better type safety
  interface ClientInfo {
    // Basic device info
    device: string;
    os: string;
    browser: string;
    browserLanguage: string;
    screenSize: string;

    // Time metrics
    timeOnSite: number;
    sessionDuration: number;

    // Referral info
    referrer: string;
    entryPage: string;
    currentPage: string;
    currentPageTitle: string;

    // Marketing parameters
    utmSource: string;
    utmMedium: string;
    utmCampaign: string;
    utmTerm: string;
    utmContent: string;
    searchKeywords: string;

    // Visit metrics
    visitCount: number;
    pagesViewedCount: number;
    isBounce: boolean;
    daysFromFirstVisit: number;

    // Performance metrics
    pageLoadTime: number;
    firstContentfulPaint: number;
    largestContentfulPaint: number;
    timeToInteractive: number;
    cumulativeLayoutShift: number;

    // Location info
    country: string;
    region: string;
    localTime: string;

    // User journey
    lastVisit: string;
    navigationPath: string[];
    maxScrollDepth: number;

    // Engagement metrics
    interactionsCount: number;
    ctaClicks: number;
    formInteractions: number;
    timeToFirstInteraction: number;

    // Conversion data
    conversionPath: string[];
    timeToConversion: number;
    previousSites: string[];
    returningVisitorConversion: boolean;
  }

  // Client information tracking
  const [clientInfo, setClientInfo] = useState<ClientInfo>({
    // Basic device info
    device: '',
    os: '',
    browser: '',
    browserLanguage: '',
    screenSize: '',

    // Time metrics
    timeOnSite: 0,
    sessionDuration: 0,

    // Referral info
    referrer: '',
    entryPage: '',
    currentPage: window.location.pathname,
    currentPageTitle: document.title,

    // Marketing parameters
    utmSource: '',
    utmMedium: '',
    utmCampaign: '',
    utmTerm: '',
    utmContent: '',
    searchKeywords: '',

    // Visit metrics
    visitCount: 0,
    pagesViewedCount: 0,
    isBounce: true,
    daysFromFirstVisit: 0,

    // Performance metrics
    pageLoadTime: 0,
    firstContentfulPaint: 0,
    largestContentfulPaint: 0,
    timeToInteractive: 0,
    cumulativeLayoutShift: 0,

    // Location info
    country: '',
    region: '',
    localTime: '',

    // User journey
    lastVisit: '',
    navigationPath: [],
    maxScrollDepth: 0,

    // Engagement metrics
    interactionsCount: 0,
    ctaClicks: 0,
    formInteractions: 0,
    timeToFirstInteraction: 0,

    // Conversion data
    conversionPath: [],
    timeToConversion: 0,
    previousSites: [],
    returningVisitorConversion: false
  });

  // Track user interactions with the page
  useEffect(() => {
    // Track general interactions (clicks, scrolls, etc.)
    const trackInteraction = () => {
      try {
        // Increment interaction count
        let interactionsCount = 1;
        const storedInteractions = localStorage.getItem('interactionsCount');
        if (storedInteractions) {
          interactionsCount = parseInt(storedInteractions, 10) + 1;
        }
        localStorage.setItem('interactionsCount', interactionsCount.toString());

        // Track time to first interaction if not already set
        if (!localStorage.getItem('timeToFirstInteraction')) {
          const sessionStartTime = sessionStorage.getItem('sessionStartTime');
          if (sessionStartTime) {
            const timeToInteraction = Date.now() - parseInt(sessionStartTime, 10);
            localStorage.setItem('timeToFirstInteraction', timeToInteraction.toString());
          }
        }

        // Update client info state
        setClientInfo(prev => ({
          ...prev,
          interactionsCount
        }));
      } catch (e) {
        console.error('Error tracking interaction:', e);
      }
    };

    // Track CTA clicks
    const trackCtaClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      // Check if the clicked element is a button, link, or has a role of button
      if (
        target.tagName === 'BUTTON' ||
        target.tagName === 'A' ||
        target.getAttribute('role') === 'button' ||
        target.classList.contains('cta')
      ) {
        try {
          let ctaClicks = 1;
          const storedCtaClicks = localStorage.getItem('ctaClicks');
          if (storedCtaClicks) {
            ctaClicks = parseInt(storedCtaClicks, 10) + 1;
          }
          localStorage.setItem('ctaClicks', ctaClicks.toString());

          // Update client info state
          setClientInfo(prev => ({
            ...prev,
            ctaClicks
          }));
        } catch (e) {
          console.error('Error tracking CTA click:', e);
        }
      }
    };

    // Track form interactions
    const trackFormInteraction = () => {
      try {
        let formInteractions = 1;
        const storedFormInteractions = localStorage.getItem('formInteractions');
        if (storedFormInteractions) {
          formInteractions = parseInt(storedFormInteractions, 10) + 1;
        }
        localStorage.setItem('formInteractions', formInteractions.toString());

        // Update client info state
        setClientInfo(prev => ({
          ...prev,
          formInteractions
        }));
      } catch (e) {
        console.error('Error tracking form interaction:', e);
      }
    };

    // Add event listeners
    document.addEventListener('click', trackInteraction);
    document.addEventListener('scroll', trackInteraction);
    document.addEventListener('click', trackCtaClick);

    // Add form interaction listeners to form elements
    const formElements = document.querySelectorAll('input, textarea, select');
    formElements.forEach(element => {
      element.addEventListener('focus', trackFormInteraction);
    });

    // Cleanup
    return () => {
      document.removeEventListener('click', trackInteraction);
      document.removeEventListener('scroll', trackInteraction);
      document.removeEventListener('click', trackCtaClick);

      formElements.forEach(element => {
        element.removeEventListener('focus', trackFormInteraction);
      });
    };
  }, []);

  // Track time on site and collect client information
  useEffect(() => {
    // Record performance metrics
    let pageLoadTime = 0;
    let firstContentfulPaint = 0;
    let largestContentfulPaint = 0;
    let timeToInteractive = 0;
    let cumulativeLayoutShift = 0;

    // Get performance metrics if available
    if (window.performance) {
      if (window.performance.timing) {
        pageLoadTime = (window.performance.timing.domContentLoadedEventEnd - window.performance.timing.navigationStart) / 1000;
      }

      // Try to get more detailed performance metrics
      try {
        const perfEntries = window.performance.getEntriesByType('paint');
        const fcpEntry = perfEntries.find(entry => entry.name === 'first-contentful-paint');
        const lcpEntry = perfEntries.find(entry => entry.name === 'largest-contentful-paint');

        if (fcpEntry) {
          firstContentfulPaint = fcpEntry.startTime / 1000;
        }

        if (lcpEntry) {
          largestContentfulPaint = lcpEntry.startTime / 1000;
        }

        // Estimate TTI (simplified)
        if (window.performance.timing.domInteractive) {
          timeToInteractive = (window.performance.timing.domInteractive - window.performance.timing.navigationStart) / 1000;
        }

        // We can't reliably measure CLS without the Layout Instability API
        // This is just a placeholder
        cumulativeLayoutShift = 0;
      } catch (e) {
        console.error('Error getting detailed performance metrics:', e);
      }
    }

    // Get device information
    const getDeviceInfo = (): ClientInfo => {
      const userAgent = navigator.userAgent;

      // Detect device type
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
      const isTablet = /(tablet|ipad|playbook|silk)|(android(?!.*mobi))/i.test(userAgent);
      const device = isTablet ? 'Tablet' : isMobile ? 'Mobile' : 'Desktop';

      // Detect operating system
      let os = 'Unknown';
      if (/Windows/i.test(userAgent)) os = 'Windows';
      else if (/Macintosh|Mac OS X/i.test(userAgent)) os = 'macOS';
      else if (/Linux/i.test(userAgent)) os = 'Linux';
      else if (/Android/i.test(userAgent)) os = 'Android';
      else if (/iPhone|iPad|iPod/i.test(userAgent)) os = 'iOS';

      // Detect browser
      let browser = 'Unknown';
      if (/Edge/i.test(userAgent)) browser = 'Edge';
      else if (/Chrome/i.test(userAgent)) browser = 'Chrome';
      else if (/Firefox/i.test(userAgent)) browser = 'Firefox';
      else if (/Safari/i.test(userAgent)) browser = 'Safari';
      else if (/MSIE|Trident/i.test(userAgent)) browser = 'Internet Explorer';

      // Browser language
      const browserLanguage = navigator.language || 'Unknown';

      // Screen size
      const screenSize = `${window.screen.width}x${window.screen.height}`;

      // Referrer and entry page
      const referrer = document.referrer || 'Direct';
      const entryPage = sessionStorage.getItem('entryPage') || window.location.pathname;

      // Save entry page if this is the first page in the session
      if (!sessionStorage.getItem('entryPage')) {
        sessionStorage.setItem('entryPage', window.location.pathname);
      }

      // Get current page title
      const currentPageTitle = document.title;

      // Get UTM parameters from URL
      const urlParams = new URLSearchParams(window.location.search);
      const utmSource = urlParams.get('utm_source') || '';
      const utmMedium = urlParams.get('utm_medium') || '';
      const utmCampaign = urlParams.get('utm_campaign') || '';
      const utmTerm = urlParams.get('utm_term') || '';
      const utmContent = urlParams.get('utm_content') || '';

      // Try to extract search keywords from referrer or UTM parameters
      let searchKeywords = '';
      if (utmTerm) {
        searchKeywords = utmTerm;
      } else if (referrer.includes('google.com/search') || referrer.includes('bing.com/search')) {
        try {
          const referrerUrl = new URL(referrer);
          searchKeywords = referrerUrl.searchParams.get('q') || '';
        } catch (e) {
          console.error('Error parsing referrer URL:', e);
        }
      }

      // Calculate pages viewed count and session duration
      let pagesViewedCount = 1;
      let sessionDuration = 0;
      let isBounce = true;

      try {
        // Get pages viewed from session storage
        const storedPagesViewed = sessionStorage.getItem('pagesViewedCount');
        if (storedPagesViewed) {
          pagesViewedCount = parseInt(storedPagesViewed, 10) + 1;
          isBounce = false; // If they've viewed more than one page, it's not a bounce
        }
        sessionStorage.setItem('pagesViewedCount', pagesViewedCount.toString());

        // Calculate session duration
        const sessionStartTime = sessionStorage.getItem('sessionStartTime');
        if (sessionStartTime) {
          sessionDuration = Math.floor((Date.now() - parseInt(sessionStartTime, 10)) / 1000);
        } else {
          sessionStorage.setItem('sessionStartTime', Date.now().toString());
        }
      } catch (e) {
        console.error('Error managing session data:', e);
      }

      // Track visit count, last visit, and engagement metrics using localStorage
      let visitCount = 1;
      let lastVisit = 'First visit';
      let daysFromFirstVisit = 0;
      let interactionsCount = 0;
      let ctaClicks = 0;
      let formInteractions = 0;
      let timeToFirstInteraction = 0;
      let timeToConversion = 0;
      let returningVisitorConversion = false;
      let previousSites: string[] = [];
      let conversionPath: string[] = [];

      try {
        // Basic visit tracking
        const storedVisitCount = localStorage.getItem('visitCount');
        const storedLastVisit = localStorage.getItem('lastVisit');
        const storedFirstVisit = localStorage.getItem('firstVisit');

        if (storedVisitCount) {
          visitCount = parseInt(storedVisitCount, 10) + 1;
        }

        if (storedLastVisit) {
          const lastVisitDate = new Date(parseInt(storedLastVisit, 10));
          lastVisit = formatNorwegianDate(lastVisitDate);
        }

        // Calculate days since first visit
        if (storedFirstVisit) {
          const firstVisitDate = new Date(parseInt(storedFirstVisit, 10));
          const today = new Date();
          const diffTime = Math.abs(today.getTime() - firstVisitDate.getTime());
          daysFromFirstVisit = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

          // If this isn't their first visit and they're submitting the form,
          // mark as a returning visitor conversion
          if (visitCount > 1) {
            returningVisitorConversion = true;
          }
        } else {
          // First visit
          localStorage.setItem('firstVisit', Date.now().toString());
        }

        // Get engagement metrics if stored
        const storedInteractions = localStorage.getItem('interactionsCount');
        if (storedInteractions) {
          interactionsCount = parseInt(storedInteractions, 10);
        }

        const storedCtaClicks = localStorage.getItem('ctaClicks');
        if (storedCtaClicks) {
          ctaClicks = parseInt(storedCtaClicks, 10);
        }

        const storedFormInteractions = localStorage.getItem('formInteractions');
        if (storedFormInteractions) {
          formInteractions = parseInt(storedFormInteractions, 10);
        }

        const storedTimeToFirstInteraction = localStorage.getItem('timeToFirstInteraction');
        if (storedTimeToFirstInteraction) {
          timeToFirstInteraction = parseInt(storedTimeToFirstInteraction, 10) / 1000;
        }

        // Get conversion data
        const storedConversionPath = localStorage.getItem('conversionPath');
        if (storedConversionPath) {
          conversionPath = JSON.parse(storedConversionPath);
        }

        // Calculate time to conversion if this is a form submission
        const storedFirstVisitTime = localStorage.getItem('firstVisit');
        if (storedFirstVisitTime) {
          timeToConversion = (Date.now() - parseInt(storedFirstVisitTime, 10)) / 1000;
        }

        // Get previous sites from referrer history if available
        const storedPreviousSites = localStorage.getItem('previousSites');
        if (storedPreviousSites) {
          previousSites = JSON.parse(storedPreviousSites);
        }

        // Store referrer as previous site if it's from a different domain
        if (referrer && !referrer.includes(window.location.hostname) && referrer !== 'Direct') {
          try {
            const referrerUrl = new URL(referrer);
            if (!previousSites.includes(referrerUrl.hostname)) {
              previousSites.push(referrerUrl.hostname);
              localStorage.setItem('previousSites', JSON.stringify(previousSites));
            }
          } catch (e) {
            console.error('Error parsing referrer for previous sites:', e);
          }
        }

        // Update localStorage
        localStorage.setItem('visitCount', visitCount.toString());
        localStorage.setItem('lastVisit', Date.now().toString());

        // Add current page to conversion path
        if (!conversionPath.includes(window.location.pathname)) {
          conversionPath.push(window.location.pathname);
          localStorage.setItem('conversionPath', JSON.stringify(conversionPath));
        }
      } catch (e) {
        console.error('Error accessing localStorage for advanced metrics:', e);
      }

      // Get user's local time in Norwegian format (yyyy.MM.dd, Kl.HH:mm)
      const now = new Date();
      const localTime = formatNorwegianDate(now);

      // Get navigation path from session storage
      let navigationPath: string[] = [];
      try {
        const storedPath = sessionStorage.getItem('navigationPath');
        if (storedPath) {
          navigationPath = JSON.parse(storedPath);
        }

        // Add current page to navigation path if it's different from the last one
        if (navigationPath.length === 0 || navigationPath[navigationPath.length - 1] !== window.location.pathname) {
          navigationPath.push(window.location.pathname);
          sessionStorage.setItem('navigationPath', JSON.stringify(navigationPath));
        }
      } catch (e) {
        console.error('Error managing navigation path:', e);
      }

      // Try to get country/region information
      let country = 'Unknown';
      let region = 'Unknown';

      // Use browser language to detect country
      if (navigator.language) {
        const langParts = navigator.language.split('-');
        if (langParts.length > 1) {
          // Map country codes to full country names
          const countryMap: Record<string, string> = {
            'NO': 'Norge',
            'US': 'USA',
            'GB': 'Storbritannia',
            'SE': 'Sverige',
            'DK': 'Danmark',
            'FI': 'Finland',
            'DE': 'Tyskland',
            'FR': 'Frankrike',
            'ES': 'Spania',
            'IT': 'Italia',
            'NL': 'Nederland'
          };

          const countryCode = langParts[1].toUpperCase();
          country = countryMap[countryCode] || countryCode;
        } else if (langParts[0] === 'nb' || langParts[0] === 'nn' || langParts[0] === 'no') {
          country = 'Norge';
        }
      }

      // Try to get timezone information as a fallback
      try {
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        if (timezone.includes('Europe/Oslo')) {
          country = 'Norge';
        } else if (timezone.includes('Europe/')) {
          region = timezone.replace('Europe/', '');
        } else if (timezone.includes('/')) {
          region = timezone.split('/')[1];
        }
      } catch (e) {
        console.error('Error getting timezone:', e);
      }

      return {
        // Basic device info
        device,
        os,
        browser,
        browserLanguage,
        screenSize,

        // Time metrics
        timeOnSite: 0, // Will be updated by the timer
        sessionDuration,

        // Referral info
        referrer,
        entryPage,
        currentPage: window.location.pathname,
        currentPageTitle,

        // Marketing parameters
        utmSource,
        utmMedium,
        utmCampaign,
        utmTerm,
        utmContent,
        searchKeywords,

        // Visit metrics
        visitCount,
        pagesViewedCount,
        isBounce,
        daysFromFirstVisit,

        // Performance metrics
        pageLoadTime,
        firstContentfulPaint,
        largestContentfulPaint,
        timeToInteractive,
        cumulativeLayoutShift,

        // Location info
        country,
        region,
        localTime,

        // User journey
        lastVisit,
        navigationPath,
        maxScrollDepth: 0,

        // Engagement metrics
        interactionsCount,
        ctaClicks,
        formInteractions,
        timeToFirstInteraction,

        // Conversion data
        conversionPath,
        timeToConversion,
        previousSites,
        returningVisitorConversion
      };
    };

    // Set initial device info
    setClientInfo(prev => ({
      ...prev,
      ...getDeviceInfo()
    }));

    // Track scroll depth
    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const scrollHeight = document.documentElement.scrollHeight;
      const clientHeight = document.documentElement.clientHeight;

      // Calculate scroll percentage
      const scrollPercentage = Math.round((scrollTop / (scrollHeight - clientHeight)) * 100);

      // Update max scroll depth if current scroll is deeper
      if (scrollPercentage > clientInfo.maxScrollDepth) {
        setClientInfo(prev => ({
          ...prev,
          maxScrollDepth: scrollPercentage
        }));
      }
    };

    // Start timer for time on site
    const startTime = Date.now();
    const interval = setInterval(() => {
      const timeSpent = Math.floor((Date.now() - startTime) / 1000);
      setClientInfo(prev => ({
        ...prev,
        timeOnSite: timeSpent
      }));
    }, 1000);

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll);

    return () => {
      clearInterval(interval);
      window.removeEventListener('scroll', handleScroll);
    };
  }, [clientInfo.maxScrollDepth]);

  const validateField = (name: string, value: string): string => {
    switch (name) {
      case 'name':
        return value.trim().length < 2 ? 'Navn må være minst 2 tegn' : '';
      case 'email':
        return !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value) ? 'Ugyldig e-postadresse' : '';
      case 'phone':
        return !/^(\+47)?[2-9]\d{7}$/.test(value.replace(/\s/g, '')) ? 'Ugyldig telefonnummer (8 siffer)' : '';
      case 'address':
        return value.trim().length < 5 ? 'Vennligst skriv inn full adresse' : '';
      case 'description':
        return value.trim().length < 10 ? 'Beskrivelsen må være minst 10 tegn' : '';
      default:
        return '';
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Validate on change if field has been touched
    if (touched[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: validateField(name, value)
      }));
    }
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setTouched(prev => ({ ...prev, [name]: true }));
    setErrors(prev => ({
      ...prev,
      [name]: validateField(name, value)
    }));
  };

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitError(null);

    // Validate all fields
    const newErrors: FormErrors = {};
    Object.keys(formData).forEach(key => {
      const error = validateField(key, formData[key as keyof FormData]);
      if (error) {
        newErrors[key as keyof FormErrors] = error;
      }
    });

    // Mark all fields as touched
    setTouched(Object.keys(formData).reduce((acc, key) => ({ ...acc, [key]: true }), {}));
    setErrors(newErrors);

    // If no errors, submit the form
    if (Object.keys(newErrors).length === 0) {
      setIsSubmitting(true);

      try {
        // Send the form data to Formspree
        const response = await fetch('https://formspree.io/f/mnndvpda', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify({
            // Form data
            name: formData.name,
            email: formData.email,
            phone: formData.phone,
            address: formData.address,
            message: formData.description,

            // Client information
            _subject: CONTACT_INFO.form.defaultSubject,

            // Add client metadata in a formatted way
            clientInfo: `
Device: ${clientInfo.device}
OS: ${clientInfo.os}
Browser: ${clientInfo.browser}
Language: ${clientInfo.browserLanguage}
Screen: ${clientInfo.screenSize}
Country: ${clientInfo.country}
Region: ${clientInfo.region || 'Not detected'}
Local Time: ${clientInfo.localTime}
Referrer: ${clientInfo.referrer}
Visit Count: ${clientInfo.visitCount}
Days Since First Visit: ${clientInfo.daysFromFirstVisit}
            `.trim(),

            // Add performance data
            performanceInfo: `
Page Load Time: ${clientInfo.pageLoadTime.toFixed(2)}s
First Contentful Paint: ${clientInfo.firstContentfulPaint.toFixed(2)}s
Largest Contentful Paint: ${clientInfo.largestContentfulPaint.toFixed(2)}s
Time to Interactive: ${clientInfo.timeToInteractive.toFixed(2)}s
Cumulative Layout Shift: ${clientInfo.cumulativeLayoutShift.toFixed(3)}
            `.trim(),

            // Add user behavior data
            behaviorInfo: `
Time on Site: ${Math.floor(clientInfo.timeOnSite / 60)}m ${clientInfo.timeOnSite % 60}s
Session Duration: ${Math.floor(clientInfo.sessionDuration / 60)}m ${clientInfo.sessionDuration % 60}s
Pages Viewed: ${clientInfo.pagesViewedCount}
Bounce: ${clientInfo.isBounce ? 'Yes' : 'No'}
Max Scroll Depth: ${clientInfo.maxScrollDepth}%
Entry Page: ${clientInfo.entryPage}
Current Page: ${clientInfo.currentPage} (${clientInfo.currentPageTitle})
Last Visit: ${clientInfo.lastVisit}
Navigation Path: ${clientInfo.navigationPath.join(' → ')}
            `.trim(),

            // Add engagement metrics
            engagementInfo: `
Total Interactions: ${clientInfo.interactionsCount}
CTA Clicks: ${clientInfo.ctaClicks}
Form Interactions: ${clientInfo.formInteractions}
Time to First Interaction: ${clientInfo.timeToFirstInteraction.toFixed(2)}s
            `.trim(),

            // Add conversion data
            conversionInfo: `
Conversion Path: ${clientInfo.conversionPath.join(' → ')}
Time to Conversion: ${Math.floor(clientInfo.timeToConversion / 60)}m ${Math.floor(clientInfo.timeToConversion % 60)}s
Returning Visitor Conversion: ${clientInfo.returningVisitorConversion ? 'Yes' : 'No'}
Previous Sites: ${clientInfo.previousSites.length > 0 ? clientInfo.previousSites.join(', ') : 'None detected'}
            `.trim(),

            // Add marketing/UTM data if available
            marketingInfo: `
${clientInfo.searchKeywords ? `Search Keywords: ${clientInfo.searchKeywords}` : ''}
${clientInfo.utmSource ? `Source: ${clientInfo.utmSource}` : ''}
${clientInfo.utmMedium ? `Medium: ${clientInfo.utmMedium}` : ''}
${clientInfo.utmCampaign ? `Campaign: ${clientInfo.utmCampaign}` : ''}
${clientInfo.utmTerm ? `Term: ${clientInfo.utmTerm}` : ''}
${clientInfo.utmContent ? `Content: ${clientInfo.utmContent}` : ''}
${!clientInfo.utmSource && !clientInfo.searchKeywords ? 'No UTM parameters or search keywords detected' : ''}
            `.trim()
          })
        });

        const data = await response.json();

        if (response.ok) {
          // Only show success message if the form was actually submitted successfully
          console.log('Form submitted successfully:', data);
          setIsSubmitted(true);
          // Reset form data for when they want to submit again
          setFormData({
            name: '',
            email: '',
            phone: '',
            address: '',
            description: ''
          });
        } else {
          // Show error message if submission failed
          console.error('Form submission error:', data);
          setSubmitError(data.error || CONTACT_INFO.form.errorMessage);
        }
      } catch (error) {
        // Show error message if there was a network error or other exception
        console.error('Form submission error:', error);
        setSubmitError(CONTACT_INFO.form.errorMessage);
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const getInputClassName = (fieldName: keyof FormData) => {
    const baseClasses = "w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent";
    return `${baseClasses} ${
      touched[fieldName] && errors[fieldName]
        ? "border-red-500 text-red-900 placeholder-red-300"
        : "border-gray-300"
    }`;
  };

  return (
    <div>
      <Meta
        title="Kontakt oss"
        description="Ta kontakt med oss for en uforpliktende befaring eller for å diskutere ditt prosjekt. Vi er her for å hjelpe deg med å skape ditt drømmeuterom."
        keywords={["kontakt", "befaring", "anleggsgartner", "hageprosjekt"]}
      />
      <Hero
        title="Kontakt oss"
        subtitle="Vi er her for å hjelpe deg med ditt neste prosjekt"
        backgroundImage="/images/hero/hero-services-granite.webp"
      />

      <Container className="py-12">
        <div className="max-w-2xl mx-auto">
          {isSubmitted ? (
            <div className="text-center py-12 bg-green-50 rounded-lg border border-green-100">
              <div className="flex justify-center mb-4">
                <div className="rounded-full bg-green-500 p-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              </div>
              <h2 className="text-2xl font-semibold text-green-700 mb-4">Meldingen er sendt!</h2>
              <p className="text-gray-600 mb-6">
                Takk for din henvendelse. Vi har mottatt din melding og vil ta kontakt med deg så snart som mulig.
              </p>
              <button
                onClick={() => setIsSubmitted(false)}
                className="bg-green-500 text-white px-6 py-2 rounded-md hover:bg-green-600 transition-colors"
              >
                Send en ny henvendelse
              </button>
            </div>
          ) : (
            <>
              <h2 className="text-2xl sm:text-3xl font-semibold mb-2">
                Kom i kontakt <span className="text-green-500">med oss</span>
              </h2>
              <p className="text-gray-600 mb-8">
                Har du spørsmål, ønsker en befaring eller trenger hjelp? Ikke nøl med å kontakte oss!
              </p>
              <form onSubmit={handleSubmit} className="space-y-4" noValidate>
                <div className="space-y-1">
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    placeholder="Navn *"
                    className={getInputClassName('name')}
                    required
                  />
                  {touched.name && errors.name && (
                    <p className="text-red-500 text-sm mt-1">{errors.name}</p>
                  )}
                </div>

                <div className="space-y-1">
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    placeholder="E-post *"
                    className={getInputClassName('email')}
                    required
                  />
                  {touched.email && errors.email && (
                    <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                  )}
                </div>

                <div className="space-y-1">
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    placeholder="Telefonnummer *"
                    className={getInputClassName('phone')}
                    required
                  />
                  {touched.phone && errors.phone && (
                    <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
                  )}
                </div>

                <div className="space-y-1">
                  <input
                    type="text"
                    name="address"
                    value={formData.address}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    placeholder="Adresse *"
                    className={getInputClassName('address')}
                    required
                  />
                  {touched.address && errors.address && (
                    <p className="text-red-500 text-sm mt-1">{errors.address}</p>
                  )}
                </div>

                <div className="space-y-1">
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    placeholder={`${CONTACT_INFO.form.defaultMessage} *`}
                    rows={4}
                    className={getInputClassName('description')}
                    required
                  />
                  {touched.description && errors.description && (
                    <p className="text-red-500 text-sm mt-1">{errors.description}</p>
                  )}
                </div>

                {submitError && (
                  <div className="p-3 bg-red-50 text-red-700 rounded-md mb-4">
                    {submitError}
                  </div>
                )}

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className={`w-full sm:w-auto bg-green-500 text-white px-8 py-3 rounded-md transition-colors ${
                    isSubmitting ? 'opacity-70 cursor-not-allowed' : 'hover:bg-green-600'
                  }`}
                >
                  {isSubmitting ? 'Sender...' : 'Send melding'}
                </button>
              </form>
            </>
          )}
        </div>
      </Container>
    </div>
  );
};

export default ContactPage;