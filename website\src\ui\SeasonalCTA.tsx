import React from "react";
import { Link } from "react-router-dom";
import { useSeasonalData } from '@/lib/hooks';
import { logSeasonalAccess } from '@/lib/utils/debug';
import { PRIMARY_AREA } from '@/lib/constants/locations';

interface SeasonalCTAProps {
    season?: "spring" | "summer" | "fall" | "winter";
    className?: string;
}

const SeasonalCTA: React.FC<SeasonalCTAProps> = ({
    season,
    className = "",
}) => {
    // Use our new seasonal hook to get seasonal data
    const {
        currentSeason,
        currentSeasonEnglish
    } = useSeasonalData();

    // Log seasonal access for debugging
    logSeasonalAccess('SeasonalCTA', currentSeason, {
        providedSeason: season
    });

    // If a specific season is provided via prop, use that; otherwise use the detected season
    const activeSeasonKey = season || currentSeasonEnglish;

    const seasonData = {
        spring: {
            title: `Planlegg for ${PRIMARY_AREA.name}-v<PERSON><PERSON>`,
            description:
                `<PERSON><PERSON>ren er perfekt for å planlegge neste sesongs hageprosjekter. Vi har inngående kjennskap til lokale forhold i ${PRIMARY_AREA.name}.`,
            icon: "🌱",
            services: [
                `Vårklargjøring i ${PRIMARY_AREA.name}`,
                "Drenering for lokalt jordsmonn",
                "Tidlig plantesesong",
            ],
            cta: `Book gratis befaring i ${PRIMARY_AREA.name}`,
        },
        summer: {
            title: "Nyt sommeren i en vakker hage",
            description:
                "Få mest mulig ut av sommeren med en velstelt og innbydende hage. Vi hjelper deg med vedlikehold og forbedringer.",
            icon: "☀️",
            services: [
                "Vanningsløsninger for tørre perioder",
                "Skyggeplanlegging med lokale arter",
                "Vedlikehold av grøntarealer",
                "Beplantning og stell",
            ],
            cta: `Book gratis befaring i ${PRIMARY_AREA.name}`,
        },
        fall: {
            title: "Høstklargjøring og planlegging",
            description:
                "Forbered hagen for vinteren og planlegg neste års prosjekter. Nå er det perfekt tid for å plante trær og busker.",
            icon: "🍂",
            services: [
                "Høstbeplantning",
                "Beskjæring",
                "Vinterklargjøring",
                `Beskyttelse mot ${PRIMARY_AREA.adjectiveForm} vinter`,
            ],
            cta: `Book gratis befaring i ${PRIMARY_AREA.name}`,
        },
        winter: {
            title: `Planlegg for ${PRIMARY_AREA.name}-våren`,
            description:
                `Vinteren er perfekt for å planlegge neste sesongs hageprosjekter. Vi har inngående kjennskap til lokale forhold i ${PRIMARY_AREA.name}.`,
            icon: "❄️",
            services: [
                "Planlegging for våren",
                "Snømåking og adkomst",
                "Vintervedlikehold av uteplasser",
                "Design og tegningstjenester",
            ],
            cta: `Book gratis befaring i ${PRIMARY_AREA.name}`,
        },
    };

    // Get the content for the active season
    const { title, description, icon, services, cta } =
        seasonData[activeSeasonKey];

    return (
        <div
            className={`bg-white rounded-lg shadow-sm overflow-hidden ${className}`}
        >
            <div className="p-6 space-y-4">
                <div className="flex items-center gap-2 text-sm text-blue-600">
                    <span>{icon}</span>
                    <span>Sesong: {currentSeason}</span>
                </div>

                <h3 className="text-xl font-semibold">{title}</h3>

                <p className="text-gray-600 text-sm">{description}</p>

                <div className="space-y-2 py-2">
                    <p className="text-gray-700 font-medium text-sm">
                        Aktuelle tjenester:
                    </p>
                    <ul className="space-y-1">
                        {services.map((service, index) => (
                            <li
                                key={index}
                                className="flex items-center gap-2 text-sm"
                            >
                                <span className="text-green-500">•</span>
                                <span>{service}</span>
                            </li>
                        ))}
                    </ul>
                </div>

                <Link
                    to="/kontakt"
                    className="block w-full bg-green-500 text-white text-center py-3 rounded-md hover:bg-green-600 transition-colors"
                >
                    {cta}
                </Link>
            </div>
        </div>
    );
};

export { SeasonalCTA };
export default SeasonalCTA;
