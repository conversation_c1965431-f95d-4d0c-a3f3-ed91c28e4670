import React from 'react';
import { Star, ExternalLink } from 'lucide-react';
import { cn } from '@/lib/utils';
import { TestimonialType } from '@/lib/types';

interface TestimonialProps {
  testimonial: TestimonialType;
  className?: string;
  variant?: 'default' | 'compact' | 'featured';
}

export const Testimonial: React.FC<TestimonialProps> = ({
  testimonial,
  className = '',
  variant = 'default'
}) => {
  const { name, location, text, rating, projectType, image, source, sourceUrl, sourceIcon } = testimonial;

  const renderStars = () => (
    <div className="flex items-center justify-between">
      <div className="flex gap-1">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={cn(
              'w-4 h-4',
              i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            )}
          />
        ))}
      </div>

      {/* Source with link - displayed next to stars */}
      {source && (
        <div className="text-xs text-gray-500">
          {sourceUrl ? (
            <a
              href={sourceUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center text-blue-600 hover:underline"
            >
              {sourceIcon && (
                <img
                  src={sourceIcon}
                  alt={source}
                  className="w-4 h-4 mr-1"
                />
              )}
              <span>{source}</span>
              <ExternalLink className="w-3 h-3 ml-1" />
            </a>
          ) : (
            <span className="flex items-center">
              {sourceIcon && (
                <img
                  src={sourceIcon}
                  alt={source}
                  className="w-4 h-4 mr-1"
                />
              )}
              <span>{source}</span>
            </span>
          )}
        </div>
      )}
    </div>
  );

  if (variant === 'compact') {
    return (
      <div className={cn('p-4 bg-white rounded-lg shadow-sm', className)}>
        <div className="mb-2">{renderStars()}</div>
        <p className="text-gray-700 text-sm my-2 line-clamp-6 overflow-hidden">
          {text}
        </p>
        <p className="font-medium text-sm">{name}</p>
      </div>
    );
  }

  if (variant === 'featured') {
    return (
      <div className={cn(
        'p-6 bg-white rounded-lg shadow-md border-l-4 border-green-500',
        className
      )}>
        <div className="mb-2">{renderStars()}</div>
        <p className="text-gray-700 my-4 italic">"{text}"</p>
        <div className="flex justify-between items-end">
          <div className="flex items-center gap-3">
            {image && (
              <img
                src={image}
                alt={name}
                className="w-10 h-10 rounded-full object-cover"
              />
            )}
            <div>
              <p className="font-medium">{name}</p>
              <p className="text-sm text-gray-500">{location}</p>
            </div>
          </div>
          <p className="text-sm text-green-600">{projectType}</p>
        </div>
      </div>
    );
  }

  // Default variant
  return (
    <div className={cn(
      'bg-white p-5 rounded-lg shadow-sm',
      className
    )} itemScope itemType="http://schema.org/Review">
      <div className="mb-3">{renderStars()}</div>
      <p className="text-gray-700 text-sm mb-3" itemProp="reviewBody">
        {text}
      </p>
      <div className="border-t pt-3">
        <div>
          <p className="font-medium text-sm" itemProp="author">{name}</p>
          <p className="text-xs text-gray-500">{location}</p>
          <p className="text-xs text-green-600" itemProp="itemReviewed">
            {projectType}
          </p>
        </div>
      </div>
    </div>
  );
};

export default Testimonial;