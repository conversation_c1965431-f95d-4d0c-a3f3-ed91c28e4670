import React from "react";
import { Link } from "react-router-dom";
import { MapPin } from "lucide-react";
import { ServiceArea  } from '@/lib/types';

interface ServiceAreaListProps {
    areas: ServiceArea[];
    className?: string;
}

const ServiceAreaList: React.FC<ServiceAreaListProps> = ({
    areas,
    className = "",
}) => {
    return (
        <div
            className={`bg-white rounded-lg shadow-sm p-6 ${className}`}
            itemScope
            itemType="http://schema.org/LocalBusiness"
            role="region"
            aria-label="Service area information"
        >
            <h3 className="text-xl font-semibold mb-4">Våre serviceområder</h3>
            <p className="text-gray-600 mb-6">
                Vi tilbyr våre tjenester i følgende områder i Ringerike og
                omegn:
            </p>

            <div className="space-y-4">
                {areas.map((area, index) => (
                    <div
                        key={index}
                        className={`flex items-start p-3 rounded-md ${
                            area.isBase
                                ? "bg-green-50 border-l-4 border-green-500"
                                : "hover:bg-gray-50"
                        }`}
                    >
                        <MapPin className="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
                        <div>
                            <div className="flex items-center gap-2">
                                <h4 className="font-medium">{area.city}</h4>
                                <span className="text-sm text-gray-500">
                                    {area.distance}
                                </span>
                                {area.isBase && (
                                    <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">
                                        Hovedområde
                                    </span>
                                )}
                            </div>
                            {area.description && (
                                <p className="text-sm text-gray-600 mt-1">
                                    {area.description}
                                </p>
                            )}
                        </div>
                    </div>
                ))}
            </div>

            <div className="mt-6 text-center">
                <Link
                    to="/kontakt"
                    className="text-green-600 hover:text-green-700 font-medium text-sm"
                >
                    Kontakt oss for tjenester i ditt område
                </Link>
            </div>
        </div>
    );
};

export { ServiceAreaList };
export default ServiceAreaList;
