
import { Zap, ThumbsUp, MessageSquare } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import <PERSON> from "@/ui/Hero";
import Container from "@/ui/Container";
import { Meta } from "@/layout/Meta";
import { CONTACT_INFO, getEmailLink } from "@/lib/constants/contact";
import { PRIMARY_AREA } from "@/lib/constants/locations";
import { ABOUT_PAGE } from "@/lib/constants/page-content";

const AboutPage = () => {
    // Use team members from contact information
    const teamMembers = CONTACT_INFO.team;

    // Define feature icons
    const featureIcons = [
        <Zap className="w-8 h-8" />,
        <ThumbsUp className="w-8 h-8" />,
        <MessageSquare className="w-8 h-8" />
    ];

    // Combine feature content with icons
    const features = ABOUT_PAGE.features.map((feature, index) => ({
        icon: featureIcons[index],
        title: feature.title,
        description: feature.description
    }));

    return (
        <>
            <Meta
                title={ABOUT_PAGE.meta.title}
                description={ABOUT_PAGE.meta.description}
                image={ABOUT_PAGE.meta.image}
                keywords={[
                    ...ABOUT_PAGE.meta.keywords,
                    PRIMARY_AREA.name
                ]}
            />
            <div>
                <Hero
                    title={ABOUT_PAGE.hero.title}
                    subtitle={ABOUT_PAGE.hero.subtitle}
                    backgroundImage={ABOUT_PAGE.meta.image}
                />

                <Container className="py-8 sm:py-12">
                    {/* About Section */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center mb-12">
                        <div className="relative h-[200px] sm:h-[300px] rounded-lg overflow-hidden">
                            <img
                                src="/images/team/ringerikelandskap-firma.webp"
                                alt="Ringerike Landskap kontor"
                                className="absolute inset-0 w-full h-full object-cover"
                                loading="lazy"
                            />
                        </div>
                        <div>
                            <h2 className="text-2xl sm:text-3xl font-semibold mb-4">
                                {ABOUT_PAGE.main.title.split(" ")[0]}{" "}
                                <span className="text-green-500">
                                    {ABOUT_PAGE.main.title.split(" ").slice(1).join(" ")}
                                </span>
                            </h2>
                            <p className="text-gray-600 mb-6">
                                {ABOUT_PAGE.main.description}
                            </p>
                            <p className="text-gray-600 mb-8">
                                {ABOUT_PAGE.main.contactText}
                            </p>
                            <Link
                                to="/kontakt"
                                className="inline-block bg-green-500 text-white px-6 py-3 rounded-md hover:bg-green-600 transition-colors"
                            >
                                {ABOUT_PAGE.main.ctaText}
                            </Link>
                        </div>
                    </div>

                    {/* Team Section */}
                    <div className="mb-12">
                        <h2 className="text-2xl sm:text-3xl font-semibold mb-8 text-center">
                            {ABOUT_PAGE.team.title.split(" ")[0]} <span className="text-green-500">{ABOUT_PAGE.team.title.split(" ")[1]}</span>
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                            {teamMembers.map((member, index) => (
                                <div
                                    key={index}
                                    className="bg-white rounded-lg overflow-hidden shadow-lg"
                                >
                                    <div className="relative h-[300px] sm:h-[400px]">
                                        <img
                                            src={member.image}
                                            alt={member.name}
                                            className="absolute inset-0 w-full h-full object-cover"
                                            loading="lazy"
                                        />
                                    </div>
                                    <div className="p-6">
                                        <h3 className="text-xl font-semibold mb-1">
                                            {member.name}
                                        </h3>
                                        <p className="text-green-500 mb-4">
                                            {member.title}
                                        </p>
                                        <div className="space-y-2">
                                            <p className="text-gray-600">
                                                {member.phone}
                                            </p>
                                            <a
                                                href={getEmailLink(member.id)}
                                                className="text-green-500 hover:text-green-600"
                                            >
                                                {member.email}
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Features Section */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        {features.map((feature, index) => (
                            <div key={index} className="text-center">
                                <div className="inline-block p-3 bg-green-100 rounded-full mb-4 text-green-500">
                                    {feature.icon}
                                </div>
                                <h3 className="text-xl font-semibold mb-3">
                                    {feature.title}
                                </h3>
                                <p className="text-gray-600">
                                    {feature.description}
                                </p>
                            </div>
                        ))}
                    </div>
                </Container>
            </div>
        </>
    );
};

export default AboutPage;
