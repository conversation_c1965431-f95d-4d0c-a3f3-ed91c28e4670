import React, { useState, useEffect, useCallback } from 'react';
import { ArrowLeft, ArrowRight } from 'lucide-react';
import { TestimonialType } from '@/lib/types';
import Testimonial from './Testimonial';
import { cn } from '@/lib/utils';

interface TestimonialSliderProps {
  testimonials: TestimonialType[];
  className?: string;
  title?: string;
  subtitle?: string;
  itemsPerView?: number;
}

export const TestimonialSlider: React.FC<TestimonialSliderProps> = ({
  testimonials,
  className = '',
  title,
  subtitle,
  itemsPerView = 1
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  const handlePrevious = useCallback(() => {
    if (isAnimating || testimonials.length <= itemsPerView) return;
    setIsAnimating(true);
    setCurrentIndex((prev) =>
      prev === 0 ? Math.max(0, testimonials.length - itemsPerView) : prev - 1
    );
    setTimeout(() => setIsAnimating(false), 500);
  }, [isAnimating, testimonials.length, itemsPerView]);

  const handleNext = useCallback(() => {
    if (isAnimating || testimonials.length <= itemsPerView) return;
    setIsAnimating(true);
    setCurrentIndex((prev) =>
      prev >= testimonials.length - itemsPerView ? 0 : prev + 1
    );
    setTimeout(() => setIsAnimating(false), 500);
  }, [isAnimating, testimonials.length, itemsPerView]);

  // Auto-advance the slider
  useEffect(() => {
    const interval = setInterval(handleNext, 6000);
    return () => clearInterval(interval);
  }, [handleNext]);

  // Reset current index when testimonials or itemsPerView changes
  useEffect(() => {
    setCurrentIndex(0);
  }, [testimonials, itemsPerView]);

  // If we don't have enough testimonials to fill the slider, don't show navigation
  const showNavigation = testimonials.length > itemsPerView;

  // Calculate which testimonials to display
  const visibleTestimonials = () => {
    const endIndex = Math.min(
      currentIndex + itemsPerView,
      testimonials.length
    );
    return testimonials.slice(currentIndex, endIndex);
  };

  // Calculate total number of pages
  const totalPages = Math.ceil(testimonials.length / itemsPerView);
  const currentPage = Math.floor(currentIndex / itemsPerView);

  if (testimonials.length === 0) {
    return null;
  }

  return (
    <div className={className}>
      {(title || subtitle) && (
        <div className="text-center mb-8">
          {title && (
            <h3 className="text-xl font-semibold mb-2">{title}</h3>
          )}
          {subtitle && (
            <p className="text-gray-600 text-sm">{subtitle}</p>
          )}
        </div>
      )}

      <div className="relative">
        {/* Navigation buttons */}
        {showNavigation && (
          <>
            <button
              onClick={handlePrevious}
              className="absolute top-1/2 -left-4 -translate-y-1/2 p-2 rounded-full bg-white shadow-md text-gray-600 hover:text-gray-900 transition-colors z-10"
              aria-label="Forrige"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <button
              onClick={handleNext}
              className="absolute top-1/2 -right-4 -translate-y-1/2 p-2 rounded-full bg-white shadow-md text-gray-600 hover:text-gray-900 transition-colors z-10"
              aria-label="Neste"
            >
              <ArrowRight className="w-5 h-5" />
            </button>
          </>
        )}

        {/* Testimonial slider */}
        <div
          className="grid gap-4 transition-all duration-500"
          style={{
            display: 'grid',
            gridTemplateColumns: `repeat(${itemsPerView}, minmax(0, 1fr))`,
            gap: '1rem',
            transform: `translateX(-${currentIndex * (100 / itemsPerView)}%)`
          }}
        >
          {visibleTestimonials().map((testimonial, index) => (
            <div key={index} className="w-full">
              <Testimonial testimonial={testimonial} />
            </div>
          ))}
        </div>

        {/* Pagination dots */}
        {totalPages > 1 && (
          <div className="flex justify-center gap-2 mt-6">
            {[...Array(totalPages)].map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index * itemsPerView)}
                className={cn(
                  'w-2 h-2 rounded-full transition-all',
                  index === currentPage
                    ? 'bg-green-500 w-4'
                    : 'bg-gray-300'
                )}
                aria-label={`Gå til side ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default TestimonialSlider;