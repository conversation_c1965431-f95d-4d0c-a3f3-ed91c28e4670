import React from "react";
import { Link } from "react-router-dom";
import { MapPin } from "lucide-react";
import { ServiceArea  } from '@/lib/types';

interface ServiceAreaListProps {
    areas: ServiceArea[];
    className?: string;
}

const ServiceAreaList: React.FC<ServiceAreaListProps> = ({
    areas,
    className = "",
}) => {
    return (
        <div
            className={`bg-white rounded-lg shadow-sm p-3 sm:p-4 ${className}`}
            itemScope
            itemType="http://schema.org/LocalBusiness"
            role="region"
            aria-label="Service area information"
        >
            <h3 className="text-xl font-semibold mb-1 sm:mb-2">Våre serviceområder</h3>
            <p className="text-gray-600 mb-2 sm:mb-3 text-sm">
                Vi tilbyr komplette anleggsgartner- og maskinentreprenørtjenester for hage, gårdsplass og uteområder i følgende områder:
            </p>

            <div className="space-y-1 sm:space-y-2">
                {areas.map((area, index) => (
                    <div
                        key={index}
                        className={`flex items-start p-1.5 sm:p-2 rounded-md ${
                            area.isBase
                                ? "bg-green-50 border-l-4 border-green-500"
                                : "hover:bg-gray-50"
                        }`}
                    >
                        <MapPin className="w-4 h-4 text-green-500 mt-0.5 mr-1.5 sm:mr-2 flex-shrink-0" />
                        <div className="flex-1">
                            <div className="flex items-center gap-1 flex-wrap">
                                <h4 className="font-medium text-sm">{area.city}</h4>
                                <span className="text-xs text-gray-500">
                                    {area.distance}
                                </span>
                                {area.isBase && (
                                    <span className="text-xs bg-green-100 text-green-800 px-1.5 py-0.5 rounded-full">
                                        Hovedområde
                                    </span>
                                )}
                            </div>
                            {area.description && (
                                <p className="text-xs sm:text-sm text-gray-600 mt-0.5">
                                    {area.description}
                                </p>
                            )}
                        </div>
                    </div>
                ))}
            </div>

            <div className="mt-3 sm:mt-4 mb-1 sm:mb-2 text-center">
                <p className="text-gray-600 text-xs sm:text-sm mb-1.5 sm:mb-2">
                    Ta kontakt for en uforpliktende prat i Røyse, Hole, Vik, Sundvollen, Hønefoss, Jevnaker og Bærum.
                </p>
                <Link
                    to="/kontakt"
                    className="text-green-600 hover:text-green-700 font-medium text-xs sm:text-sm inline-block"
                >
                    Kontakt oss for tjenester i ditt område
                </Link>
            </div>
        </div>
    );
};

export { ServiceAreaList };
export default ServiceAreaList;
