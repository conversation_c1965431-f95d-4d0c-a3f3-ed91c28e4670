/**
 * Seasonal utilities for working with season-based data and functionality
 *
 * This module provides utilities for determining the current season,
 * formatting season names, and working with seasonal data.
 */

import { SeasonType, ServiceType, ProjectType } from '@/lib/types';
import {
  MONTH_TO_SEASON,
  SEASON_DISPLAY_NAMES,
  SEASON_MAPPING,
  SEASONAL_SERVICES,
  SEASONAL_PROJECTS
} from '@/lib/constants/seasonal';
import { normalizeString, normalizedStringCompare } from './strings';

/**
 * Gets the current season based on the current month
 * @returns The current season ('vinter', 'vår', 'sommer', or 'høst')
 */
export const getCurrentSeason = (): SeasonType => {
  const month = new Date().getMonth();
  return MONTH_TO_SEASON[month];
};

/**
 * Gets the display name for a season
 * @param season The season to get the display name for
 * @returns The display name for the season (e.g., 'Våren' for 'vår')
 */
export const getSeasonDisplayName = (season: SeasonType): string => {
  return SEASON_DISPLAY_NAMES[season] || season;
};

/**
 * Converts a Norwegian season name to English
 * @param season The Norwegian season name
 * @returns The English season name
 */
export const getEnglishSeasonName = (season: SeasonType): string => {
  return SEASON_MAPPING[season] || season;
};

/**
 * Checks if a service matches a season
 * @param service The service to check
 * @param season The season to check against
 * @param strictMode Whether to use strict matching (primarily for winter season)
 * @returns Whether the service matches the season
 */
export const serviceMatchesSeason = (
  service: ServiceType,
  season: SeasonType,
  strictMode = false
): boolean => {
  const seasonMapping = SEASONAL_SERVICES[season];
  if (!seasonMapping) return false;

  // Extract the main category from the service title
  const serviceMainCategory = getServiceMainCategory(service);

  // Check for exact category match
  const exactCategoryMatch = seasonMapping.categories.some(category =>
    normalizeString(serviceMainCategory) === normalizeString(category) ||
    normalizeString(service.title).startsWith(normalizeString(category))
  );

  // If we're in strict mode (for winter) or we have an exact match, return the result
  if (strictMode || exactCategoryMatch) {
    return exactCategoryMatch;
  }

  // For non-strict mode and no exact match, check for feature matches
  return service.features.some(feature =>
    seasonMapping.features.some(seasonFeature =>
      normalizedStringCompare(feature, seasonFeature)
    )
  );
};

/**
 * Checks if a project matches a season
 * @param project The project to check
 * @param season The season to check against
 * @returns Whether the project matches the season
 */
export const projectMatchesSeason = (project: ProjectType, season: SeasonType): boolean => {
  const seasonMapping = SEASONAL_PROJECTS[season];
  if (!seasonMapping) return false;

  // Check if project category matches any of the seasonal categories
  const categoryMatch = seasonMapping.categories.some(category =>
    normalizedStringCompare(project.category, category)
  );

  if (categoryMatch) return true;

  // Check if any project tags match seasonal tags
  return project.tags.some(tag =>
    seasonMapping.tags.some(seasonTag =>
      normalizedStringCompare(tag, seasonTag)
    )
  );
};

/**
 * Extract the main category from a service title
 * @param service The service object
 * @returns The main category (usually the first word of the title)
 */
export const getServiceMainCategory = (service: ServiceType): string => {
  const words = service.title.split(' ');
  return words.length > 1 ? words[0] : service.title;
};
